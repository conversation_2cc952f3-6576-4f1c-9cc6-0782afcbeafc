"""
Admin Service Layer for Shared API Management

This service provides a simplified interface between the Telegram bot admin handlers
and the shared API system, handling configuration management, validation, and testing.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from shared_api.config.api_config import (
    APIConfiguration,
    EndpointConfiguration,
    AuthenticationConfiguration,
    TimeoutConfiguration,
    RetryConfiguration
)
from shared_api.config.client_factory import api_client_factory
from shared_api.config.registry import api_registry
from shared_api.core.constants import HTTPMethod, AuthenticationType
from shared_api.core.exceptions import ConfigurationError, HTTPClientError
from shared_api.utils.validation import ConfigurationValidator
from admin.models.api_config_storage import (
    get_admin_api_service,
    AdminAPIConfiguration,
    APIConfigEnvironment,
    APIConfigStatus
)

logger = logging.getLogger(__name__)


class SharedAPIAdminService:
    """
    Admin service for managing shared API configurations through Telegram bot interface
    
    This service provides a simplified, admin-friendly interface for:
    - Creating and managing API configurations
    - Testing API connectivity and health
    - Validating configurations
    - Managing the shared API registry
    """
    
    def __init__(self):
        self.admin_storage = get_admin_api_service()
    
    async def create_api_configuration(
        self,
        name: str,
        base_url: str,
        created_by: str,
        display_name: Optional[str] = None,
        description: Optional[str] = None,
        auth_type: str = "none",
        auth_data: Optional[Dict[str, Any]] = None,
        endpoints: Optional[Dict[str, Dict[str, Any]]] = None,
        environment: str = "development",
        **kwargs
    ) -> Tuple[bool, str, Optional[AdminAPIConfiguration]]:
        """
        Create a new API configuration
        
        Args:
            name: Unique API name
            base_url: Base URL for the API
            created_by: Telegram user ID of creator
            display_name: Human-readable name
            description: API description
            auth_type: Authentication type (none, api_key, bearer_token, etc.)
            auth_data: Authentication configuration data
            endpoints: Endpoint configurations
            environment: Environment (development, staging, production)
            **kwargs: Additional configuration options
            
        Returns:
            Tuple of (success, message, admin_config)
        """
        try:
            # Validate basic inputs
            if not name or not name.strip():
                return False, "API name is required", None
            
            if not base_url or not base_url.strip():
                return False, "Base URL is required", None
            
            # Check if API already exists
            existing = await self.admin_storage.get_api_config(name)
            if existing:
                return False, f"API configuration '{name}' already exists", None
            
            # Create authentication configuration
            auth_config = None
            if auth_type != "none" and auth_data:
                auth_config = self._create_auth_config(auth_type, auth_data)
                if not auth_config:
                    return False, f"Invalid authentication configuration for type '{auth_type}'", None
            
            # Create endpoint configurations
            endpoint_configs = {}
            if endpoints:
                for ep_name, ep_data in endpoints.items():
                    endpoint_configs[ep_name] = EndpointConfiguration(
                        name=ep_name,
                        path=ep_data.get("path", f"/{ep_name}"),
                        method=HTTPMethod(ep_data.get("method", "GET")),
                        description=ep_data.get("description", "")
                    )
            
            # Create shared API configuration
            shared_config = APIConfiguration(
                name=name,
                base_url=base_url.rstrip("/"),
                endpoints=endpoint_configs,
                authentication=auth_config,
                timeout=TimeoutConfiguration(
                    connect=kwargs.get("connect_timeout", 10),
                    read=kwargs.get("read_timeout", 30),
                    total=kwargs.get("total_timeout", 60)
                ),
                retry=RetryConfiguration(
                    max_attempts=kwargs.get("max_retries", 3),
                    delay=kwargs.get("retry_delay", 1.0),
                    backoff_factor=kwargs.get("backoff_factor", 2.0)
                )
            )
            
            # Validate the configuration
            is_valid, errors = ConfigurationValidator.validate_api_config(shared_config.to_dict())
            if not is_valid:
                return False, f"Configuration validation failed: {'; '.join(errors)}", None
            
            # Create admin configuration
            admin_config = await self.admin_storage.create_api_config(
                shared_config=shared_config,
                created_by=created_by,
                display_name=display_name or name,
                description=description or "",
                environment=APIConfigEnvironment(environment),
                status=APIConfigStatus.ACTIVE,
                category=kwargs.get("category", "general"),
                tags=kwargs.get("tags", [])
            )
            
            return True, f"Successfully created API configuration '{name}'", admin_config
            
        except Exception as e:
            logger.error(f"Failed to create API configuration '{name}': {e}")
            return False, f"Failed to create configuration: {str(e)}", None
    
    async def update_api_configuration(
        self,
        name: str,
        updated_by: str,
        **update_fields
    ) -> Tuple[bool, str]:
        """
        Update an existing API configuration
        
        Args:
            name: API name to update
            updated_by: Telegram user ID of updater
            **update_fields: Fields to update
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Get existing configuration
            admin_config = await self.admin_storage.get_api_config(name)
            if not admin_config:
                return False, f"API configuration '{name}' not found"
            
            # Get current shared configuration
            shared_config = admin_config.to_shared_config()
            
            # Update fields
            changes = []
            
            if "base_url" in update_fields:
                shared_config.base_url = update_fields["base_url"].rstrip("/")
                changes.append("base_url")
            
            if "auth_type" in update_fields or "auth_data" in update_fields:
                auth_type = update_fields.get("auth_type", shared_config.auth_config.type if shared_config.auth_config else "none")
                auth_data = update_fields.get("auth_data", {})
                
                if auth_type != "none":
                    shared_config.auth_config = self._create_auth_config(auth_type, auth_data)
                    changes.append("authentication")
                else:
                    shared_config.auth_config = AuthenticationConfiguration()
                    changes.append("authentication")
            
            if "endpoints" in update_fields:
                endpoint_configs = {}
                for ep_name, ep_data in update_fields["endpoints"].items():
                    endpoint_configs[ep_name] = EndpointConfiguration(
                        name=ep_name,
                        path=ep_data.get("path", f"/{ep_name}"),
                        method=HTTPMethod(ep_data.get("method", "GET")),
                        description=ep_data.get("description", "")
                    )
                shared_config.endpoints = endpoint_configs
                changes.append("endpoints")
            
            # Validate updated configuration
            is_valid, errors = ConfigurationValidator.validate_api_config(shared_config.to_dict())
            if not is_valid:
                return False, f"Configuration validation failed: {'; '.join(errors)}"
            
            # Update in storage
            success = await self.admin_storage.update_api_config(
                name=name,
                shared_config=shared_config,
                updated_by=updated_by,
                changes_description=f"Updated: {', '.join(changes)}"
            )
            
            if success:
                return True, f"Successfully updated API configuration '{name}'"
            else:
                return False, f"Failed to update API configuration '{name}'"
            
        except Exception as e:
            logger.error(f"Failed to update API configuration '{name}': {e}")
            return False, f"Failed to update configuration: {str(e)}"
    
    async def test_api_connection(
        self,
        name: str,
        endpoint_name: Optional[str] = None
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        Test API connectivity and health
        
        Args:
            name: API name to test
            endpoint_name: Specific endpoint to test (optional)
            
        Returns:
            Tuple of (success, message, test_results)
        """
        try:
            # Get configuration
            admin_config = await self.admin_storage.get_api_config(name)
            if not admin_config:
                return False, f"API configuration '{name}' not found", None
            
            # Get shared configuration and create client
            shared_config = admin_config.to_shared_config()
            
            try:
                client = api_client_factory.create_client(shared_config)
            except Exception as e:
                return False, f"Failed to create API client: {str(e)}", None
            
            test_results = {
                "api_name": name,
                "test_time": datetime.utcnow().isoformat(),
                "tests": []
            }
            
            # Test specific endpoint or health check
            if endpoint_name:
                if endpoint_name not in shared_config.endpoints:
                    return False, f"Endpoint '{endpoint_name}' not found in configuration", None
                
                # Test specific endpoint
                start_time = asyncio.get_event_loop().time()
                try:
                    async with client:
                        response = await client.get(endpoint_name)
                    
                    response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                    
                    test_results["tests"].append({
                        "endpoint": endpoint_name,
                        "success": True,
                        "response_time_ms": response_time,
                        "status": "healthy"
                    })
                    
                    # Update health status
                    admin_config.update_health_status("healthy", response_time_ms=response_time)
                    
                    return True, f"Endpoint '{endpoint_name}' is healthy (response time: {response_time:.1f}ms)", test_results
                    
                except Exception as e:
                    test_results["tests"].append({
                        "endpoint": endpoint_name,
                        "success": False,
                        "error": str(e),
                        "status": "unhealthy"
                    })
                    
                    # Update health status
                    admin_config.update_health_status("unhealthy", error_message=str(e))
                    
                    return False, f"Endpoint '{endpoint_name}' test failed: {str(e)}", test_results
            
            else:
                # General health check
                start_time = asyncio.get_event_loop().time()
                try:
                    async with client:
                        is_healthy = await client.health_check()
                    
                    response_time = (asyncio.get_event_loop().time() - start_time) * 1000
                    
                    if is_healthy:
                        test_results["tests"].append({
                            "type": "health_check",
                            "success": True,
                            "response_time_ms": response_time,
                            "status": "healthy"
                        })
                        
                        # Update health status
                        admin_config.update_health_status("healthy", response_time_ms=response_time)
                        
                        return True, f"API '{name}' is healthy (response time: {response_time:.1f}ms)", test_results
                    else:
                        test_results["tests"].append({
                            "type": "health_check",
                            "success": False,
                            "status": "unhealthy",
                            "error": "Health check returned false"
                        })
                        
                        # Update health status
                        admin_config.update_health_status("unhealthy", error_message="Health check failed")
                        
                        return False, f"API '{name}' health check failed", test_results
                        
                except Exception as e:
                    test_results["tests"].append({
                        "type": "health_check",
                        "success": False,
                        "error": str(e),
                        "status": "unhealthy"
                    })
                    
                    # Update health status
                    admin_config.update_health_status("unhealthy", error_message=str(e))
                    
                    return False, f"API '{name}' test failed: {str(e)}", test_results
            
        except Exception as e:
            logger.error(f"Failed to test API '{name}': {e}")
            return False, f"Test failed: {str(e)}", None
    

    
    async def get_api_details(self, name: str) -> Optional[Dict[str, Any]]:
        """Get detailed API configuration information"""
        try:
            admin_config = await self.admin_storage.get_api_config(name)
            if not admin_config:
                return None
            
            shared_config = admin_config.to_shared_config()
            
            return {
                "admin_info": admin_config.to_admin_summary(),
                "shared_config": shared_config.to_dict(),
                "endpoints": {
                    name: {
                        "name": ep.name,
                        "path": ep.path,
                        "method": ep.method,
                        "description": ep.description
                    }
                    for name, ep in shared_config.endpoints.items()
                },
                "authentication": {
                    "type": shared_config.auth_config.type if shared_config.auth_config else "none",
                    "has_credentials": bool(shared_config.auth_config)
                },
                "audit_trail": admin_config.audit_trail[-10:]  # Last 10 entries
            }
            
        except Exception as e:
            logger.error(f"Failed to get API details for '{name}': {e}")
            return None
    
    def _create_auth_config(
        self,
        auth_type: str,
        auth_data: Dict[str, Any]
    ) -> Optional[AuthenticationConfiguration]:
        """Create authentication configuration from admin input"""
        try:
            if auth_type == "api_key":
                return AuthenticationConfiguration(
                    type=AuthenticationType.API_KEY,
                    api_key=auth_data.get("api_key"),
                    api_key_header=auth_data.get("api_key_header", "X-API-Key"),
                    custom_headers=auth_data.get("custom_headers", {})
                )
            elif auth_type == "bearer_token":
                return AuthenticationConfiguration(
                    type=AuthenticationType.BEARER_TOKEN,
                    bearer_token=auth_data.get("bearer_token")
                )
            elif auth_type == "basic_auth":
                return AuthenticationConfiguration(
                    type=AuthenticationType.BASIC_AUTH,
                    username=auth_data.get("username"),
                    password=auth_data.get("password")
                )
            elif auth_type == "custom_header":
                return AuthenticationConfiguration(
                    type=AuthenticationType.CUSTOM_HEADER,
                    custom_headers=auth_data.get("custom_headers", {})
                )
            else:
                return None
                
        except Exception as e:
            logger.error(f"Failed to create auth config for type '{auth_type}': {e}")
            return None

    async def get_api_audit_log(self, name: str) -> List[Dict[str, Any]]:
        """Get audit log for an API configuration"""
        try:
            admin_config = await self.admin_storage.get_api_config(name)
            if not admin_config:
                return []

            # Return the audit trail with formatted entries
            audit_entries = []
            for entry in admin_config.audit_trail:
                audit_entries.append({
                    "timestamp": entry.get("timestamp", ""),
                    "action": entry.get("action", ""),
                    "user": entry.get("user", ""),
                    "details": entry.get("details", ""),
                    "changes": entry.get("changes", {})
                })

            return audit_entries

        except Exception as e:
            logger.error(f"Failed to get audit log for API '{name}': {e}")
            return []

    async def search_api_configurations(self, search_term: str) -> List[Dict[str, Any]]:
        """Search API configurations by name, description, or URL"""
        try:
            all_configs = await self.admin_storage.list_api_configs()
            search_term_lower = search_term.lower()

            matching_configs = []
            for config in all_configs:
                # Search in name, display_name, description, and base_url
                if (search_term_lower in config.name.lower() or
                    search_term_lower in config.display_name.lower() or
                    search_term_lower in config.description.lower() or
                    search_term_lower in config.to_shared_config().base_url.lower()):
                    matching_configs.append(config)

            return [config.to_admin_summary() for config in matching_configs]

        except Exception as e:
            logger.error(f"Failed to search API configurations: {e}")
            return []

    async def list_api_configurations(
        self,
        environment: Optional[str] = None,
        enabled_only: bool = False,
        status_filter: Optional[str] = None,
        category_filter: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        List API configurations for admin UI with filtering options

        Args:
            environment: Filter by environment
            enabled_only: Only return enabled configurations
            status_filter: Filter by status (enabled, disabled)
            category_filter: Filter by category

        Returns:
            List of API configuration summaries
        """
        try:
            admin_configs = await self.admin_storage.list_api_configs(
                environment=environment,
                enabled_only=enabled_only
            )

            # Apply additional filters
            if status_filter:
                if status_filter == "enabled":
                    admin_configs = [c for c in admin_configs if c.enabled]
                elif status_filter == "disabled":
                    admin_configs = [c for c in admin_configs if not c.enabled]

            if category_filter:
                admin_configs = [c for c in admin_configs if c.category == category_filter]

            # Ensure all configs are registered in the shared registry
            await self._ensure_registry_sync(admin_configs)

            return [config.to_admin_summary() for config in admin_configs]

        except Exception as e:
            logger.error(f"Failed to list API configurations: {e}")
            return []

    async def update_api_status(self, api_name: str, enabled: bool) -> bool:
        """Update API enabled/disabled status"""
        try:
            admin_config = await self.admin_storage.get_api_config(api_name)
            if not admin_config:
                return False

            admin_config.enabled = enabled
            admin_config.updated_by = "admin_system"

            # Add audit entry
            admin_config.add_audit_entry(
                user_id="admin_system",
                action="status_updated",
                changes={"enabled": enabled},
                success=True
            )

            # Save to database
            success = await self.admin_storage.update_api_config(admin_config)

            if success:
                # Update shared registry
                shared_config = admin_config.to_shared_config()
                api_registry.register_api(shared_config)
                logger.info(f"Updated API {api_name} status to {'enabled' if enabled else 'disabled'}")

            return success

        except Exception as e:
            logger.error(f"Failed to update API status for {api_name}: {e}")
            return False

    async def ensure_api_v2_enabled(self) -> bool:
        """Ensure API v2 is properly configured and enabled"""
        try:
            # Check if API v2 configuration exists
            api_v2_config = await self.admin_storage.get_api_config("api2")

            if not api_v2_config:
                # Create default API v2 configuration
                from api_v2.config.api_config import create_api_v2_configuration
                from admin.models.api_config_storage import AdminAPIConfiguration, APIConfigStatus

                shared_config = create_api_v2_configuration()

                api_v2_config = AdminAPIConfiguration(
                    name="api2",
                    display_name="API v2 - BASE 2 Browse API",
                    description="Secondary BIN API with VHQ endpoints",
                    shared_config=shared_config.to_dict(),
                    environment="production",
                    status=APIConfigStatus.ACTIVE,
                    enabled=True,
                    created_by="admin_system",
                    category="bin_cards",
                    tags=["api2", "base2", "vhq", "browse", "bin"]
                )

                success = await self.admin_storage.create_api_config(api_v2_config)
                if not success:
                    return False
            else:
                # Enable existing configuration
                api_v2_config.enabled = True
                api_v2_config.status = "active"
                success = await self.admin_storage.update_api_config(api_v2_config)
                if not success:
                    return False

            # Register in shared API registry
            shared_config = api_v2_config.to_shared_config()
            api_registry.register_api(shared_config)

            logger.info("API v2 enabled successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to ensure API v2 enabled: {e}")
            return False

    async def _ensure_registry_sync(self, admin_configs: List) -> None:
        """Ensure all admin configurations are registered in the shared API registry"""
        try:
            from shared_api.config.registry import api_registry

            # Get currently registered API names
            registered_names = {api["name"] for api in api_registry.list_apis()}

            # Register any missing configurations
            for admin_config in admin_configs:
                if admin_config.name not in registered_names:
                    try:
                        shared_config = admin_config.to_shared_config()
                        api_registry.register_api(shared_config)
                        logger.debug(f"Registered missing API in registry: {admin_config.name}")
                    except Exception as e:
                        logger.error(f"Failed to register API '{admin_config.name}' in registry: {e}")

        except Exception as e:
            logger.error(f"Failed to ensure registry sync: {e}")


# Global service instance
_shared_api_admin_service = None


def get_shared_api_admin_service() -> SharedAPIAdminService:
    """Get the global shared API admin service instance"""
    global _shared_api_admin_service
    if _shared_api_admin_service is None:
        _shared_api_admin_service = SharedAPIAdminService()
    return _shared_api_admin_service
