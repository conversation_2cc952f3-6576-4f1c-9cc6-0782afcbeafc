# Production Deployment Guide
## Demo Wallet Bot v2 - Security Hardened

### Overview

This guide provides comprehensive instructions for deploying the Demo Wallet Bot v2 in a production environment with enterprise-grade security, monitoring, and reliability features.

## Pre-Deployment Security Checklist

### 1. Environment Preparation
- [ ] Secure server environment (Ubuntu 20.04+ LTS recommended)
- [ ] Firewall configured (UFW or iptables)
- [ ] SSH key-based authentication only
- [ ] Regular security updates enabled
- [ ] Non-root user for application deployment
- [ ] SSL/TLS certificates obtained and configured

### 2. Database Security
- [ ] MongoDB cluster with authentication enabled
- [ ] Database user with minimal required permissions
- [ ] SSL/TLS encryption for database connections
- [ ] Regular automated backups configured
- [ ] Database access restricted to application servers only
- [ ] Connection pooling and timeout configurations

### 3. Application Security
- [ ] All secrets stored in secure environment variables
- [ ] API encryption keys generated and secured
- [ ] Admin passphrases use strong, unique values
- [ ] Rate limiting configured for production loads
- [ ] Input validation enabled for all user inputs
- [ ] Security logging and monitoring configured

## Deployment Steps

### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install -y python3.11 python3.11-venv python3-pip nginx redis-server mongodb-tools

# Create application user
sudo useradd -m -s /bin/bash botuser
sudo usermod -aG sudo botuser

# Create application directory
sudo mkdir -p /opt/demo-wallet-bot
sudo chown botuser:botuser /opt/demo-wallet-bot
```

### Step 2: Application Deployment

```bash
# Switch to application user
sudo su - botuser

# Clone and setup application
cd /opt/demo-wallet-bot
git clone <your-repository-url> .
python3.11 -m venv venv
source venv/bin/activate
pip install -r requirements.txt

# Copy production configuration
cp config.production.env .env
# Edit .env with your production values
nano .env
```

### Step 3: Security Configuration

```bash
# Generate encryption keys
python3 -c "from cryptography.fernet import Fernet; print('API_CONFIG_ENCRYPTION_KEY=' + Fernet.generate_key().decode())" >> .env

# Set secure file permissions
chmod 600 .env
chmod 755 /opt/demo-wallet-bot
chmod -R 644 /opt/demo-wallet-bot/*.py

# Create log directories
sudo mkdir -p /var/log/demo-wallet-bot
sudo chown botuser:botuser /var/log/demo-wallet-bot
sudo chmod 755 /var/log/demo-wallet-bot
```

### Step 4: Database Setup

```bash
# Connect to MongoDB and create user
mongo --ssl --host your-mongodb-cluster.example.com
use demo_wallet_bot_prod
db.createUser({
  user: "botuser",
  pwd: "secure_password_here",
  roles: [
    { role: "readWrite", db: "demo_wallet_bot_prod" },
    { role: "dbAdmin", db: "demo_wallet_bot_prod" }
  ]
})
```

### Step 5: Service Configuration

Create systemd service file:

```bash
sudo nano /etc/systemd/system/demo-wallet-bot.service
```

```ini
[Unit]
Description=Demo Wallet Bot v2
After=network.target mongodb.service redis.service
Wants=mongodb.service redis.service

[Service]
Type=simple
User=botuser
Group=botuser
WorkingDirectory=/opt/demo-wallet-bot
Environment=PATH=/opt/demo-wallet-bot/venv/bin
ExecStart=/opt/demo-wallet-bot/venv/bin/python run.py
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=demo-wallet-bot

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/demo-wallet-bot /var/log/demo-wallet-bot
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

[Install]
WantedBy=multi-user.target
```

### Step 6: Reverse Proxy Setup (Nginx)

```bash
sudo nano /etc/nginx/sites-available/demo-wallet-bot
```

```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";

    # Health check endpoint
    location /health {
        proxy_pass http://127.0.0.1:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # Metrics endpoint (restrict access)
    location /metrics {
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
        proxy_pass http://127.0.0.1:8000;
    }
}
```

### Step 7: Monitoring Setup

```bash
# Install monitoring tools
sudo apt install -y prometheus node-exporter

# Configure Prometheus
sudo nano /etc/prometheus/prometheus.yml
```

Add job configuration:
```yaml
  - job_name: 'demo-wallet-bot'
    static_configs:
      - targets: ['localhost:8000']
    scrape_interval: 15s
```

### Step 8: Log Management

```bash
# Configure log rotation
sudo nano /etc/logrotate.d/demo-wallet-bot
```

```
/var/log/demo-wallet-bot/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 botuser botuser
    postrotate
        systemctl reload demo-wallet-bot
    endscript
}
```

### Step 9: Backup Configuration

```bash
# Create backup script
sudo nano /opt/demo-wallet-bot/scripts/backup.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/demo-wallet-bot"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup database
mongodump --host your-mongodb-cluster.example.com --ssl --db demo_wallet_bot_prod --out $BACKUP_DIR/db_$DATE

# Backup application configuration
cp /opt/demo-wallet-bot/.env $BACKUP_DIR/config_$DATE.env

# Backup logs
tar -czf $BACKUP_DIR/logs_$DATE.tar.gz /var/log/demo-wallet-bot/

# Clean old backups (keep 7 days)
find $BACKUP_DIR -type f -mtime +7 -delete

echo "Backup completed: $DATE"
```

```bash
# Make executable and add to cron
chmod +x /opt/demo-wallet-bot/scripts/backup.sh
crontab -e
# Add: 0 2 * * * /opt/demo-wallet-bot/scripts/backup.sh
```

## Security Hardening

### 1. Firewall Configuration

```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

### 2. Fail2Ban Setup

```bash
sudo apt install fail2ban
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
```

### 3. System Monitoring

```bash
# Install system monitoring
sudo apt install htop iotop nethogs

# Configure system alerts
sudo nano /opt/demo-wallet-bot/scripts/health-check.sh
```

## Post-Deployment Verification

### 1. Security Tests
- [ ] Run security scan with tools like `nmap` and `nikto`
- [ ] Verify SSL/TLS configuration with `ssllabs.com`
- [ ] Test rate limiting and input validation
- [ ] Verify database access restrictions
- [ ] Check log file permissions and rotation

### 2. Performance Tests
- [ ] Load test with expected user volume
- [ ] Monitor memory and CPU usage
- [ ] Test database connection pooling
- [ ] Verify backup and restore procedures

### 3. Monitoring Verification
- [ ] Confirm metrics collection
- [ ] Test alerting mechanisms
- [ ] Verify log aggregation
- [ ] Check health check endpoints

## Maintenance Procedures

### Regular Tasks
- Weekly security updates
- Monthly backup verification
- Quarterly security audits
- Annual SSL certificate renewal
- Regular log analysis and cleanup

### Emergency Procedures
- Incident response plan
- Rollback procedures
- Database recovery steps
- Security breach protocols

## Troubleshooting

### Common Issues
1. **Database Connection Failures**: Check MongoDB cluster status and credentials
2. **High Memory Usage**: Monitor for memory leaks, adjust worker settings
3. **Rate Limiting Issues**: Review rate limit configurations and user patterns
4. **SSL Certificate Errors**: Verify certificate validity and renewal

### Log Analysis
- Application logs: `/var/log/demo-wallet-bot/bot.log`
- Security logs: `/var/log/demo-wallet-bot/security.log`
- System logs: `/var/log/syslog`
- Nginx logs: `/var/log/nginx/`

This deployment guide ensures a secure, scalable, and maintainable production environment for the Demo Wallet Bot v2.
