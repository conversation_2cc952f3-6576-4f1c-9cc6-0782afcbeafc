# External API Configuration Guide

## Overview

The checkout queue system integrates with the external website API (ronaldo-club.to) to manage cart operations and process checkouts. This guide explains how to configure the external API credentials and settings.

## Required Configuration

### Environment Variables

Add the following environment variables to your `.env` file:

```bash
# External API Authentication
EXTERNAL_LOGIN_TOKEN=your_jwt_token_here                        # Your JWT token
EXTERNAL_DDG1=your_session_cookie_here                          # Session cookie
EXTERNAL_DDG8=your_session_cookie_here                          # Session cookie
EXTERNAL_DDG9=your_ip_tracking_cookie_here                      # IP tracking cookie
EXTERNAL_DDG10=your_timestamp_cookie_here                       # Timestamp cookie
EXTERNAL_GA=your_google_analytics_cookie_here                   # Google Analytics
EXTERNAL_GA_KZWCRF57VT=your_ga_session_cookie_here              # GA session
```

### Obtaining Credentials

1. **Login Token (JWT)**:
   - Log into ronaldo-club.to in your browser
   - Open Developer Tools (F12)
   - Go to Application/Storage → Cookies
   - Copy the `loginToken` value

2. **Session Cookies**:
   - While logged in, copy all `__ddg*` cookie values
   - These cookies maintain your session state

3. **Google Analytics Cookies**:
   - Copy `_ga` and `_ga_KZWCRF57VT` values
   - These help maintain consistent browser fingerprinting

## API Endpoints

The system uses the following external API endpoints:

### Cart Management
- **View Cart**: `GET https://ronaldo-club.to/api/cart/`
- **Add Item**: `POST https://ronaldo-club.to/api/cart/`
- **Remove Item**: `DELETE https://ronaldo-club.to/api/cart/{item_id}`

### Checkout
- **Process Checkout**: `POST https://ronaldo-club.to/api/checkout/`

## Request Format Examples

### Add Item to Cart
```json
POST https://ronaldo-club.to/api/cart/
Content-Type: application/json

{
    "id": 1682634,
    "product_table_name": "Cards"
}
```

### View Cart Contents
```bash
GET https://ronaldo-club.to/api/cart/
Accept: application/json, text/plain, */*
```

### Remove Item from Cart
```bash
DELETE https://ronaldo-club.to/api/cart/12345
Accept: application/json, text/plain, */*
```

## Headers Configuration

The system automatically includes these headers with all requests:

```python
{
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9",
    "Content-Type": "application/json",
    "Origin": "https://ronaldo-club.to",
    "Referer": "https://ronaldo-club.to/store/cart",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36..."
}
```

## Error Handling

### Common HTTP Status Codes

- **200**: Success
- **400**: Bad Request (invalid cart data)
- **401**: Unauthorized (invalid/expired token)
- **403**: Forbidden (insufficient permissions)
- **404**: Not Found (item doesn't exist)
- **500**: Server Error

### Authentication Issues

If you receive 401/403 errors:

1. **Check Token Expiry**: JWT tokens have expiration times
2. **Refresh Session**: Log out and log back in to get new tokens
3. **Update Cookies**: Copy fresh cookie values from browser
4. **Verify Permissions**: Ensure account has cart access

### Rate Limiting

The system includes built-in rate limiting:
- 0.1 second delay between cart operations
- Timeout settings: 30s for cart ops, 60s for checkout
- Retry logic with exponential backoff

## Security Considerations

### Token Management
- **Never commit tokens to version control**
- **Use environment variables only**
- **Rotate tokens regularly**
- **Monitor for unauthorized usage**

### Session Security
- Tokens are tied to specific IP addresses
- Sessions may expire after inactivity
- Use HTTPS for all communications
- Validate SSL certificates

## Testing Configuration

### Verify Setup
```bash
# Run the external API integration test
python test_external_cart_integration.py
```

### Expected Test Results
- ✅ Session creation should succeed
- ⚠️ Cart operations may fail with 403 (expected without valid auth)
- ✅ Configuration validation should pass
- ⚠️ Login token warning is expected in test environment

### Debug Mode
Enable debug logging to see detailed API interactions:

```python
import logging
logging.getLogger('services.checkout_queue_service').setLevel(logging.DEBUG)
```

## Production Deployment

### Environment Setup
1. **Secure Token Storage**: Use encrypted environment variables
2. **Token Rotation**: Implement automatic token refresh
3. **Monitoring**: Set up alerts for authentication failures
4. **Backup Authentication**: Have multiple valid sessions

### Health Monitoring
- Monitor API response times
- Track authentication failure rates
- Alert on consecutive 403/401 errors
- Log all external API interactions

## Troubleshooting

### Common Issues

1. **403 Forbidden Errors**
   - Solution: Update loginToken with fresh JWT
   - Check: Verify account permissions

2. **Timeout Errors**
   - Solution: Check network connectivity
   - Check: Verify API endpoint availability

3. **Invalid Response Format**
   - Solution: Verify API hasn't changed response structure
   - Check: Log raw responses for debugging

4. **Cart Synchronization Issues**
   - Solution: Clear external cart before each operation
   - Check: Validate cart contents after operations

### Debug Commands

```bash
# Test external API connectivity
curl -H "Authorization: Bearer YOUR_TOKEN" https://ronaldo-club.to/api/cart/

# Check token validity
python -c "import jwt; print(jwt.decode('YOUR_TOKEN', verify=False))"

# Test cart operations
python test_external_cart_integration.py
```

## API Rate Limits

### Current Limits
- Cart operations: ~10 requests/second
- Checkout operations: ~1 request/10 seconds
- Session refresh: ~1 request/hour

### Best Practices
- Batch operations when possible
- Use exponential backoff on failures
- Cache cart state locally
- Minimize unnecessary API calls

## Support

For external API issues:
1. Check ronaldo-club.to status page
2. Verify account standing and permissions
3. Contact site administrators if needed
4. Review API documentation for changes

For integration issues:
1. Check application logs
2. Run integration tests
3. Verify configuration settings
4. Contact development team
