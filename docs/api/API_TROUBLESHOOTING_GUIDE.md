# API Troubleshooting Guide

This comprehensive guide helps you diagnose and resolve common API issues in the Bot v2 system.

## 🚨 Quick Diagnostics

### Check API Status
```bash
# Check all API health
python tests/validate_api_v2.py
python tests/validate_shared_api.py

# Check specific API
python -c "
import asyncio
from services.product_service import ProductService
async def check_api():
    service = ProductService()
    # Add your diagnostic code here
asyncio.run(check_api())
"
```

### View System Logs
```bash
# API request logs
tail -f logs/api_requests.log

# Error logs
tail -f logs/api_errors.log

# Authentication logs
tail -f logs/api_auth.log
```

## 🔍 Common Issues and Solutions

### 1. API v2 Not Visible to Users

**Symptoms:**
- Users can't see API v2 (BASE 2) in selection menu
- Only API v1 (BASE 1) appears in options

**Diagnosis:**
```bash
# Check API status in product configuration
python -c "
from models.product import DEFAULT_PRODUCT_CONFIG, ProductType
config = DEFAULT_PRODUCT_CONFIG
bin_product = config.get_product_by_type(ProductType.BIN)
for api in bin_product.apis:
    print(f'{api.name}: {api.status.value}')
"
```

**Solutions:**
1. **Check Product Configuration:**
   - Verify API v2 status is set to `ACTIVE` in `models/product.py`
   - Ensure `status=APIStatus.ACTIVE` for `bin_base_2`

2. **Admin Panel Quick Fix:**
   - Use admin interface: API Management → Quick Enable API v2
   - Or manually enable via admin configuration

3. **Verify Dynamic Status Loading:**
   - Check if admin API service is properly configured
   - Ensure database connection is working

### 2. Authentication Errors (HTTP 403)

**Symptoms:**
- "Login to continue..." error messages
- HTTP 403 responses from API endpoints
- Authentication failures in logs

**Diagnosis:**
```bash
# Check authentication configuration
python -c "
from api_v2.config.api_config import create_api_v2_configuration
config = create_api_v2_configuration()
print('Auth config:', config.authentication)
"
```

**Solutions:**
1. **Update API Credentials:**
   - Go to Admin Panel → API Management → Select API → Authentication
   - Update bearer token or API key
   - Test authentication after update

2. **Check Token Format:**
   - Ensure bearer token includes "Bearer " prefix
   - Verify token is not expired
   - Check for special characters or encoding issues

3. **Verify API Endpoint:**
   - Confirm base URL is correct
   - Check if API endpoint is accessible
   - Verify SSL/TLS configuration

### 3. API Endpoint Not Found (HTTP 404)

**Symptoms:**
- "The requested resource was not found" errors
- HTTP 404 responses
- Missing endpoint errors

**Diagnosis:**
```bash
# Check API configuration
python -c "
from api_v2.config.api_config import create_api_v2_configuration
config = create_api_v2_configuration()
print('Base URL:', config.base_url)
print('Endpoints:', config.endpoints)
"
```

**Solutions:**
1. **Verify Endpoint Configuration:**
   - Check `api_v2/config/api_config.py`
   - Ensure all required endpoints are defined
   - Verify endpoint paths are correct

2. **Check API Server Status:**
   - Verify external API server is running
   - Check if endpoint exists on the server
   - Test endpoint manually with curl/Postman

3. **Update Configuration:**
   - Update base URL if server moved
   - Add missing endpoints to configuration
   - Restart bot after configuration changes

### 4. Connection Timeouts

**Symptoms:**
- "Connection timeout" error messages
- Slow API responses
- Request hanging indefinitely

**Diagnosis:**
```bash
# Check network connectivity
curl -I https://ronaldo-club.to/api/cards/vhq/list

# Check timeout settings
python -c "
from shared_api.config.models import SharedAPIConfiguration
# Check timeout configuration
"
```

**Solutions:**
1. **Increase Timeout Values:**
   - Update timeout settings in API configuration
   - Consider network latency and server response time
   - Balance between responsiveness and reliability

2. **Check Network Connectivity:**
   - Verify internet connection
   - Check firewall settings
   - Test DNS resolution

3. **Implement Retry Logic:**
   - Configure automatic retries for failed requests
   - Use exponential backoff strategy
   - Set maximum retry limits

### 5. API Fallback Not Working

**Symptoms:**
- System doesn't fall back to API v1 when v2 fails
- Users stuck with non-functional API
- No automatic recovery

**Diagnosis:**
```bash
# Test fallback mechanism
python -c "
import asyncio
from services.product_service import ProductService
async def test_fallback():
    service = ProductService()
    api_service = await service.get_external_api_service_for_user(12345)
    print(f'Service type: {type(api_service).__name__}')
asyncio.run(test_fallback())
"
```

**Solutions:**
1. **Verify Fallback Logic:**
   - Check `services/product_service.py`
   - Ensure `_get_api_service_with_fallback` is working
   - Verify health check implementation

2. **Check Health Monitoring:**
   - Ensure health checks are running
   - Verify health check criteria
   - Check health check frequency

3. **Test Fallback Manually:**
   - Temporarily disable API v2
   - Verify system falls back to API v1
   - Check fallback logging

## 🔧 Advanced Diagnostics

### Performance Issues

**Check API Metrics:**
```python
from api_v2.services.browse_service import APIV2BrowseService
service = APIV2BrowseService()
metrics = service.get_performance_metrics()
print(f"Success rate: {metrics['successful_requests']/metrics['total_requests']*100:.1f}%")
print(f"Average response time: {metrics['average_response_time']:.2f}s")
```

**Monitor Resource Usage:**
```bash
# Check memory usage
ps aux | grep python

# Check network connections
netstat -an | grep :443
```

### Database Issues

**Check Database Connection:**
```python
from database.connection import get_collection
try:
    collection = get_collection("user_product_preferences")
    print("Database connected successfully")
except Exception as e:
    print(f"Database error: {e}")
```

### Configuration Validation

**Validate All Configurations:**
```bash
# Run configuration tests
python tests/test_api_v2_browse_service.py
python tests/test_shared_api.py
```

## 📊 Monitoring and Alerts

### Key Metrics to Monitor
1. **API Response Times** - Should be < 2 seconds
2. **Success Rates** - Should be > 95%
3. **Error Rates** - Should be < 5%
4. **Fallback Frequency** - Monitor for patterns

### Log Analysis
```bash
# Count API errors by type
grep "ERROR" logs/api_errors.log | cut -d' ' -f4 | sort | uniq -c

# Monitor authentication failures
grep "403" logs/api_requests.log | wc -l

# Check fallback events
grep "fallback" logs/api_requests.log
```

## 🆘 Emergency Procedures

### Complete API Failure
1. **Immediate Actions:**
   - Switch all users to API v1 (BASE 1)
   - Disable problematic API in admin panel
   - Notify users of temporary service changes

2. **Investigation:**
   - Check all logs for error patterns
   - Verify external API server status
   - Test configuration changes in development

3. **Recovery:**
   - Fix identified issues
   - Test thoroughly before re-enabling
   - Gradually re-enable for subset of users

### Data Corruption
1. **Stop all API operations**
2. **Backup current state**
3. **Investigate data integrity**
4. **Restore from known good backup**
5. **Implement additional validation**

## 📞 Getting Help

### Internal Resources
1. Check this troubleshooting guide
2. Review system logs
3. Test with diagnostic scripts
4. Check admin panel status

### External Support
1. API provider documentation
2. Network connectivity tests
3. Server status pages
4. Community forums

### Escalation Path
1. **Level 1**: Basic troubleshooting (this guide)
2. **Level 2**: Advanced diagnostics and configuration
3. **Level 3**: Code changes and architecture modifications
4. **Level 4**: External vendor support

---

**Last Updated**: 2025-09-22  
**Guide Version**: 1.0  
**Covers**: Bot v2 API Management System
