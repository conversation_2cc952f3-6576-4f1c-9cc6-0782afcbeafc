# API v2 / BASE 2 Migration Guide

This guide covers the changes introduced with the new API v2 (BASE 2) implementation
and how to adopt it alongside the existing API v1 (BASE 1) integration.

## Endpoint Differences

| Feature            | API v1 / BASE 1                         | API v2 / BASE 2 (VHQ)                        |
|--------------------|-----------------------------------------|-----------------------------------------------|
| Base URL           | `https://ronaldo-club.to/api`           | `https://ronaldo-club.to/api/cards/vhq/`      |
| List items         | `/cards/hq/list` (POST)                 | `/list` (POST)                                |
| Filter metadata    | `/cards/hq/filters` (GET)               | `/filters` (GET)                              |
| Orders             | `/cards/hq/orders` (GET)                | `/orders` (GET)                               |
| Order check        | `/cards/hq/check` (POST)                | `/check` (POST)                               |
| Referer header     | `/store/cards/hq`                       | `/store/cards/vhq`                            |
| Config name        | `api1`                                  | `api2`                                        |

All other parameters, request formats, and response payloads remain consistent with
API v1 to avoid contract changes in the UI or downstream processing.

## Configuration Steps

1. **Create the shared configuration**
   - Use `api_v2.config.api_config.create_api_v2_configuration()` or the new
     admin template ("BIN BASE 2 API").
   - Provide the VHQ base URL and bearer token captured from the authenticated session.
   - Optional: add session cookies for rate-limit hardening.

2. **Register in the shared registry**
   - The helper `shared_api.config.register_api_v2()` registers the configuration with
     the global registry.
   - `APIV2BrowseService` automatically registers its configuration when instantiated.

3. **Admin panel integration**
   - The default bootstrap now seeds both BASE 1 and BASE 2 configurations.
   - Enable or disable either API from the admin panel without redeploying code.

4. **Validation**
   - Run `venv/bin/python validate_api_v2.py` to confirm query construction and
     endpoint wiring before switching traffic.

## Switching Between APIs

1. Activate the desired API under `Settings → Products → BIN Cards` in the admin panel.
2. Ensure the product entry references `config_name = "api2"` for BASE 2.
3. The `CardService` continues to work because both APIs share the same response shape.
4. Use the validation scripts (`validate_shared_api.py`, `validate_api_v2.py`) for smoke
   tests before going live.
5. Monitor the shared API registry health checks to confirm both clients stay healthy.

## Rollback Plan

If issues occur:

- Switch the product configuration back to `api1` (BASE 1).
- Re-run `validate_shared_api.py` to confirm API v1 is healthy.
- Keep API v2 configuration registered so it can be toggled back on after fixes.

## Additional Notes

- All retry, timeout, and authentication behaviour is inherited from the shared
  infrastructure (`ConfigurableHTTPClient`).
- The new browse service keeps pagination, filtering, and payload semantics identical
  to API v1 to minimize downstream changes.
- Update the `config.production.env` secrets with the BASE 2 bearer token and
  cookies if they differ from BASE 1.
