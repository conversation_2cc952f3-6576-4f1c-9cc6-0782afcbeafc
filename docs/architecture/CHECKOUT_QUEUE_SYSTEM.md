# Checkout Queue System Documentation

## Overview

The Checkout Queue System is a comprehensive solution for managing multiple users shopping through a single shared website account. It ensures proper isolation between users while maintaining a smooth shopping experience through serialized checkout processing.

## Core Architecture

### 1. Virtual Cart Management
- **Individual virtual carts** for each Telegram user ID stored in backend database
- **Cart isolation** prevents interference between different users
- **Real-time cart synchronization** with external website API
- **Cart validation** before checkout processing

### 2. Checkout Queue System
- **FIFO processing queue** with immutable cart snapshots
- **Unique job IDs** for tracking and idempotency
- **Job status tracking** (queued, processing, completed, failed, cancelled)
- **Queue position estimation** and processing time calculation

### 3. Serialized Processing
- **Single worker thread** processing checkout jobs sequentially
- **Exclusive locks** to prevent race conditions
- **External API coordination** for shared website account
- **Atomic operations** for cart and payment processing

### 4. Idempotency & Reliability
- **UUID-based idempotency keys** to prevent duplicate order processing
- **Exponential backoff retry logic** for failed operations
- **Specific failure scenario handling** (out of stock, price changes, payment failures)
- **Dead letter queue** for permanently failed jobs

## Key Components

### CheckoutJob Class
```python
class CheckoutJob:
    - job_id: str              # Unique job identifier
    - user_id: str             # User document ID
    - cart_snapshot: Dict      # Immutable cart state at queue time
    - status: CheckoutJobStatus # Current job status
    - idempotency_key: str     # Prevents duplicate processing
    - created_at: datetime     # Job creation timestamp
    - metadata: Dict           # Additional job metadata
```

### CheckoutQueueService
- **Queue Management**: Add, cancel, and track checkout jobs
- **Worker Processing**: Background task for sequential job processing
- **External API Integration**: Manage shared website cart operations
- **Error Handling**: Retry logic and failure recovery
- **Monitoring**: Queue statistics and performance metrics

### Integration Points
- **Cart Service**: Updated to use queue system instead of direct checkout
- **Purchase Handlers**: Modified to queue orders and track status
- **User Notifications**: Real-time updates via Telegram messages
- **Startup Service**: Automatic background worker initialization

## User Flow

1. **Add Items**: User adds items to virtual cart (no external API calls)
2. **View Cart**: User reviews cart contents and total
3. **Initiate Checkout**: User clicks checkout → order queued
4. **Queue Notification**: User receives queue position and estimated time
5. **Processing Updates**: Real-time notifications about order progress
6. **Completion**: Final confirmation with order details and receipt

## Technical Implementation

### Database Schema
```javascript
// checkout_jobs collection
{
  job_id: String,           // Unique job identifier
  user_id: String,          // User document ID
  cart_snapshot: Object,    // Immutable cart state
  status: String,           // Job status enum
  idempotency_key: String,  // Prevents duplicates
  created_at: Date,         // Job creation time
  updated_at: Date,         // Last update time
  completed_at: Date,       // Completion time (if applicable)
  attempts: Number,         // Retry attempt count
  last_error: String,       // Last error message
  metadata: Object          // Additional data
}
```

### External API Operations

#### 1. Cart Clearing (`_clear_external_cart`)
- **Purpose**: Ensure complete isolation between users
- **Process**:
  - Retrieve current cart contents via GET `/api/cart/`
  - Remove each item individually via DELETE `/api/cart/{item_id}`
  - Verify cart is completely empty
- **Error Handling**: Retry on failures, log all operations

#### 2. Cart Population (`_populate_external_cart`)
- **Purpose**: Add user's items to external cart
- **Process**:
  - Add items one by one via POST `/api/cart/`
  - Handle quantity by making multiple requests
  - Include 0.1s delay between requests to avoid rate limiting
- **Payload Format**: `{"id": card_id, "product_table_name": "Cards"}`

#### 3. Cart Validation (`_validate_cart_items`)
- **Purpose**: Verify external cart matches user's snapshot
- **Process**:
  - Compare expected vs actual item counts
  - Check for stock availability indicators
  - Validate price consistency (if available)
  - Report detailed validation errors

#### 4. Checkout Execution (`_execute_external_checkout`)
- **Purpose**: Process payment through external API
- **Process**:
  - Submit POST request to `/api/checkout/`
  - Handle various response formats
  - Extract order ID from successful responses
  - Provide detailed error messages for failures

#### 5. Session Management (`_get_external_session`)
- **Purpose**: Manage HTTP sessions with proper authentication
- **Features**:
  - Automatic header and cookie management
  - Timeout configuration (30s cart, 60s checkout)
  - Connection pooling and limits
  - Proper session cleanup

### Error Handling
- **Retry Logic**: Exponential backoff (1s, 2s, 4s delays)
- **Timeout Handling**: 5-minute timeout per job
- **Stock Validation**: Handle out-of-stock scenarios
- **Payment Failures**: Proper error messaging and refunds
- **API Timeouts**: Graceful degradation and retry

## Configuration

### Queue Settings
```python
max_retries = 3                    # Maximum retry attempts
retry_delays = [1, 2, 4]          # Exponential backoff delays
job_timeout = 300                 # 5 minutes per job
queue_check_interval = 1          # Check queue every second
```

### External API Configuration
```python
external_api_url = "https://ronaldo-club.to/api"
external_cart_url = f"{external_api_url}/cart/"
external_checkout_url = f"{external_api_url}/checkout/"
```

#### Required Environment Variables
```bash
EXTERNAL_LOGIN_TOKEN=your_jwt_token_here                        # JWT token
EXTERNAL_DDG1=your_session_cookie_here                          # Session cookie
EXTERNAL_DDG8=your_session_cookie_here                          # Session cookie
EXTERNAL_DDG9=your_ip_tracking_cookie_here                      # IP tracking
EXTERNAL_DDG10=your_timestamp_cookie_here                       # Timestamp
```

#### Authentication Headers
```python
{
    "Accept": "application/json, text/plain, */*",
    "Accept-Language": "en-US,en;q=0.9",
    "Content-Type": "application/json",
    "Origin": "https://ronaldo-club.to",
    "Referer": "https://ronaldo-club.to/store/cart",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)..."
}
```

See `docs/EXTERNAL_API_CONFIGURATION.md` for detailed setup instructions.

## Monitoring & Alerting

### Queue Statistics
- Jobs by status (queued, processing, completed, failed, cancelled)
- Average processing time
- Queue length and estimated wait times
- Error rates and failure patterns

### Performance Metrics
- Job processing throughput
- API response times
- Retry attempt frequencies
- User satisfaction metrics

## Security Considerations

### Data Protection
- **Cart snapshots** are immutable to prevent tampering
- **Idempotency keys** prevent duplicate charges
- **User isolation** ensures no cross-user data leakage
- **Secure API communication** with external website

### Access Control
- **User authentication** required for all operations
- **Job ownership validation** prevents unauthorized access
- **Rate limiting** to prevent abuse
- **Audit logging** for compliance and debugging

## Deployment & Scaling

### Background Services
- **Automatic startup** with main bot application
- **Graceful shutdown** handling
- **Health monitoring** and restart capabilities
- **Resource management** and memory optimization

### Horizontal Scaling
- **Database-backed queue** supports multiple worker instances
- **Distributed locking** for job processing coordination
- **Load balancing** across multiple bot instances
- **Shared state management** via MongoDB

## Testing

### Unit Tests
- Job creation and serialization
- Queue operations and statistics
- Error handling and retry logic
- External API integration mocking

### Integration Tests
- End-to-end checkout flow
- Multi-user concurrent operations
- Failure scenario handling
- Performance under load

## Future Enhancements

### Planned Features
- **Priority queues** for premium users
- **Batch processing** for efficiency
- **Advanced analytics** and reporting
- **Machine learning** for demand prediction

### Scalability Improvements
- **Microservice architecture** separation
- **Event-driven processing** with message queues
- **Caching layers** for performance
- **Auto-scaling** based on queue length

## Troubleshooting

### Common Issues
1. **Queue stuck**: Check worker process status
2. **High failure rate**: Verify external API connectivity
3. **Slow processing**: Monitor API response times
4. **Duplicate orders**: Verify idempotency key generation

### Debug Commands
```bash
# Check queue status
python -c "from services.checkout_queue_service import CheckoutQueueService; import asyncio; asyncio.run(CheckoutQueueService().get_queue_stats())"

# Test queue functionality
python test_checkout_queue.py
```

## Support

For technical support or questions about the checkout queue system:
- Review logs in `/logs/` directory
- Check database collections for job status
- Monitor external API health endpoints
- Contact development team for assistance
