# Gemini Code Assistant Context

This document provides context for the Gemini Code Assistant to understand the project structure, conventions, and important files.

## Project Overview

This project is a standalone Telegram bot named "Demo Wallet Bot v2". It is built using Python with the `aiogram` library for Telegram Bot API interaction. The backend database is MongoDB, with a fallback to an in-memory simulation for development purposes.

The bot provides the following core features:

*   **Wallet Management**: A virtual wallet with balance tracking and a comprehensive transaction history.
*   **Card Catalog**: A browsable and filterable catalog of demo card offerings.
*   **Local Shopping Cart**: Users can add/remove items, manage quantities, and complete a checkout process.
*   **Purchase System**: A secure system for purchasing demo cards with a confirmation flow and receipt generation.
*   **Admin Panel**: A comprehensive admin panel for user management, catalog management, API configuration, and system monitoring.

The project follows a layered architecture, separating concerns into:

*   **Handlers**: Telegram bot handlers for user interactions.
*   **Services**: Business logic layer.
*   **Models**: Data models for MongoDB.
*   **Middleware**: Cross-cutting concerns like rate limiting and error handling.
*   **Utils**: Utility modules for keyboards, texts, and other helper functions.

## Building and Running

### 1. Automated Setup (Recommended)

The project includes a setup script that automates the process of setting up the virtual environment, installing dependencies, and creating the `.env` file.

```bash
# Navigate to bot_v2 directory
cd bot_v2

# Run the setup script
./setup.sh
```

### 2. Manual Setup

Alternatively, the project can be set up manually:

```bash
# Create virtual environment
python3 -m venv venv

# Activate virtual environment
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Copy example configuration
cp config.example.env .env
```

### 3. Configuration

The project is configured through a `.env` file. The following are the required and optional configuration options:

*   `BOT_TOKEN`: (Required) The Telegram Bot API token.
*   `ADMIN_USER_IDS`: (Optional) A comma-separated list of Telegram user IDs for admin access.
*   `USE_MONGODB`: (Optional) Set to `true` to use MongoDB, otherwise it will use an in-memory simulation.
*   `MONGODB_URL`: (Optional) The MongoDB connection URL.

### 4. Running the Bot

The bot can be run using the provided `run.sh` script or manually:

```bash
# Using the run script (recommended)
./run.sh

# Or manually
source venv/bin/activate && python run.py
```

## Development Conventions

*   **Asynchronous Code**: The entire codebase is asynchronous, using `async/await` syntax.
*   **Type Hinting**: The project uses type hints for all function signatures and variable declarations.
*   **Dependency Management**: Dependencies are managed using `pip` and a `requirements.txt` file.
*   **Testing**: The project includes integration tests for manual validation.
*   **Logging**: The project uses the `logging` module for structured logging.
*   **Linting and Formatting**: The project should be linted and formatted using standard Python tools like `black` and `flake8`.

## Important Files

*   `main.py`: The main application entry point.
*   `run.py`: The standalone runner script.
*   `requirements.txt`: The list of Python dependencies.
*   `config/settings.py`: The configuration management module.
*   `database/connection.py`: The MongoDB connection and database management module.
*   `handlers/`: The directory containing all the Telegram bot handlers.
*   `services/`: The directory containing the business logic services.
*   `models/`: The directory containing the data models.
*   `middleware/`: The directory containing the middleware for the bot.
*   `utils/`: The directory containing the utility modules.
*   `README.md`: The project's README file with detailed information.
*   `ARCHITECTURE.md`: The project's architecture documentation.
