# 🔧 Admin API Management System Guide

## Overview

The Admin API Management System provides a comprehensive Telegram bot interface for managing API configurations using the shared API infrastructure. This system replaces the legacy API management code with a modern, scalable solution.

## 🚀 Quick Start

### Accessing the API Management System

1. **Start the bot** and ensure you have admin privileges
2. **Use the command** `/api` to access the main API management menu
3. **Navigate** through the intuitive inline keyboard interface

### Basic Commands

- `/api` - Main API management interface
- `/api help` - Show help and available commands
- `/api status` - Show system status and health summary

## 📋 Main Features

### 1. API Configuration Management

#### Creating New APIs
1. Click **"➕ Create New API"** from the main menu
2. Follow the step-by-step wizard:
   - **Step 1**: Enter unique API name (e.g., `payment-api`, `user_service`)
   - **Step 2**: Provide base URL (e.g., `https://api.example.com`)
   - **Step 3**: Choose authentication type
   - **Step 4**: Configure endpoints (optional)

#### Ready-Made Templates
- **BIN BASE 2 (VHQ)** – Preconfigured with the new BASE 2 browse endpoints
  (`/list`, `/filters`, `/orders`, `/check`) and correct base URL
  `https://ronaldo-club.to/api/cards/vhq/`. Supply the bearer token captured from
  the live session and optionally add session cookies for rate limiting.
- **API v1 (Legacy)** – Retains the existing BASE 1 definition for fallback purposes.

#### Authentication Types Supported
- **🔓 None** - No authentication required
- **🔑 API Key** - API key in custom header
- **🎫 Bearer Token** - JWT or bearer token authentication
- **👤 Basic Auth** - Username and password
- **🔧 Custom Headers** - Custom authentication headers

#### Authentication Configuration Examples

**API Key Authentication:**
```json
{
  "api_key": "your-api-key-here",
  "api_key_header": "X-API-Key"
}
```

**Bearer Token Authentication:**
```json
{
  "bearer_token": "your-jwt-token-here"
}
```

**Basic Authentication:**
```json
{
  "username": "your-username",
  "password": "your-password"
}
```

**Custom Headers:**
```json
{
  "custom_headers": {
    "X-Custom-Auth": "value1",
    "X-Client-ID": "value2"
  }
}
```

### 2. API Testing and Health Monitoring

#### Health Checks
- **Individual API Testing**: Test specific APIs for connectivity
- **Bulk Health Monitoring**: Check all APIs at once
- **Endpoint Testing**: Test specific endpoints
- **Real-time Status**: Live health status indicators

#### Health Status Indicators
- **🟢✅** - API is healthy and responding
- **🔴❌** - API is unhealthy or not responding
- **🟡❓** - API status is unknown

#### Response Time Monitoring
- Real-time response time measurement
- Historical performance tracking
- Performance alerts for slow APIs

#### Validation Scripts
- `validate_shared_api.py` – Smoke test for baseline shared infrastructure.
- `validate_api_v2.py` – Verifies the new BASE 2 browse wiring and query
  construction before toggling traffic.

### 3. API Management Operations

#### Viewing API Details
1. Select an API from the list
2. View comprehensive information:
   - Basic configuration (name, URL, environment)
   - Authentication settings
   - Endpoint configurations
   - Health status and performance metrics
   - Usage statistics
   - Audit trail

#### Editing API Configurations
1. Navigate to API details
2. Click **"✏️ Edit"**
3. Modify settings as needed
4. Changes are automatically validated

#### Managing Endpoints
- Add new endpoints
- Modify existing endpoint paths and methods
- Test individual endpoints
- Remove unused endpoints

### 4. Environment Management

#### Supported Environments
- **🧪 Development** - For testing and development
- **🔄 Staging** - For pre-production testing
- **🚀 Production** - For live production APIs

#### Environment-Specific Features
- Filter APIs by environment
- Environment-specific configurations
- Separate health monitoring per environment

## 🔍 Advanced Features

### 1. API Registry Integration

The system automatically integrates with the shared API registry:
- **Automatic Registration**: New APIs are automatically registered
- **Real-time Sync**: Changes are immediately reflected in the registry
- **Centralized Management**: Single source of truth for all API configurations

### 2. Configuration Import/Export

#### Exporting Configurations
1. Navigate to API details
2. Click **"📤 Export"**
3. Receive JSON configuration file
4. Use for backup or migration

#### Importing Configurations
- Use migration scripts for bulk imports
- Support for legacy configuration formats
- Validation and error handling

### 3. Audit Trail and Logging

#### Audit Information Tracked
- Configuration changes
- User who made changes
- Timestamp of modifications
- Description of changes made

#### Accessing Audit Logs
1. Navigate to API details
2. Click **"📋 Audit Log"**
3. View chronological list of changes

## 🛠️ Administration Tasks

### 1. Migration from Legacy System

#### Prerequisites
- Ensure new system is properly installed
- Backup existing configurations
- Verify admin access

#### Migration Process
```bash
# 1. Run migration script (dry run first)
python admin/scripts/migrate_legacy_api_config.py --dry-run --verbose

# 2. Create backup and migrate
python admin/scripts/migrate_legacy_api_config.py --backup backups/legacy_backup.json

# 3. Verify migration success
python admin/scripts/migrate_legacy_api_config.py --verify
```

### 2. Legacy Code Cleanup

#### After Successful Migration
```bash
# 1. Validate migration success
python admin/scripts/cleanup_legacy_api_code.py --dry-run --verbose

# 2. Clean up legacy code (with backup)
python admin/scripts/cleanup_legacy_api_code.py

# 3. Force cleanup if needed
python admin/scripts/cleanup_legacy_api_code.py --force
```

### 3. System Maintenance

#### Regular Health Checks
- Monitor API health dashboard
- Review performance metrics
- Check for failed APIs
- Update configurations as needed

#### Database Maintenance
- Regular backup of API configurations
- Clean up old health check data
- Monitor storage usage

## 🚨 Troubleshooting

### Common Issues

#### API Creation Fails
**Problem**: "Configuration validation failed"
**Solution**: 
- Check URL format (must start with http:// or https://)
- Verify authentication configuration JSON format
- Ensure API name is unique

#### Health Check Failures
**Problem**: API shows as unhealthy
**Solution**:
- Verify API is actually running
- Check authentication credentials
- Test network connectivity
- Review API logs

#### Authentication Issues
**Problem**: "Authentication failed" errors
**Solution**:
- Verify credentials are correct
- Check authentication type matches API requirements
- Ensure headers are properly configured

### Error Messages

#### "API configuration not found"
- API may have been deleted
- Check API name spelling
- Verify you have access permissions

#### "Failed to create client"
- Configuration validation failed
- Check all required fields are provided
- Verify authentication configuration format

#### "Request timed out"
- API is not responding
- Network connectivity issues
- API server may be down

### Getting Help

#### Support Channels
1. **Bot Help**: Use `/api help` command
2. **Documentation**: Refer to this guide
3. **Logs**: Check application logs for detailed errors
4. **Admin Support**: Contact system administrators

#### Diagnostic Information
When reporting issues, include:
- API name and configuration
- Error messages received
- Steps to reproduce the issue
- Environment (development/staging/production)

## 📊 Best Practices

### 1. API Configuration

#### Naming Conventions
- Use descriptive, unique names
- Include version if applicable (e.g., `payment-api-v2`)
- Use lowercase with hyphens or underscores

#### URL Management
- Use HTTPS for production APIs
- Include API version in base URL if needed
- Avoid trailing slashes

#### Authentication Security
- Use environment-specific credentials
- Rotate tokens regularly
- Store sensitive data securely

### 2. Health Monitoring

#### Regular Monitoring
- Check API health daily
- Set up alerts for critical APIs
- Monitor response time trends

#### Performance Optimization
- Identify slow APIs
- Optimize endpoint configurations
- Review and update timeout settings

### 3. Configuration Management

#### Version Control
- Export configurations regularly
- Keep backups of important APIs
- Document configuration changes

#### Environment Separation
- Use separate configurations for each environment
- Test changes in development first
- Promote configurations through environments

## 🔄 Migration Guide

### From Legacy System

#### Phase 1: Preparation
1. **Backup existing configurations**
2. **Install new admin system**
3. **Verify system functionality**

#### Phase 2: Migration
1. **Run migration script with dry-run**
2. **Review migration plan**
3. **Execute actual migration**
4. **Verify all APIs migrated correctly**

#### Phase 3: Validation
1. **Test all migrated APIs**
2. **Verify health checks work**
3. **Confirm authentication works**
4. **Update any dependent systems**

#### Phase 4: Cleanup
1. **Remove legacy code**
2. **Update documentation**
3. **Train users on new system**

### Post-Migration Checklist

- [ ] All APIs migrated successfully
- [ ] Health checks working for all APIs
- [ ] Authentication working correctly
- [ ] Users trained on new interface
- [ ] Legacy code removed
- [ ] Documentation updated
- [ ] Backup procedures in place

## 📈 Monitoring and Analytics

### Health Dashboard
- Overall system health percentage
- API response time averages
- Failed API count
- Recent health check results

### Performance Metrics
- Response time trends
- Success/failure rates
- Usage statistics
- Error patterns

### Reporting
- Daily health summaries
- Weekly performance reports
- Monthly usage analytics
- Quarterly system reviews

---

## 🎯 Next Steps

After setting up the Admin API Management System:

1. **Migrate existing configurations** using the migration scripts
2. **Train team members** on the new interface
3. **Set up monitoring** and alerting
4. **Establish maintenance procedures**
5. **Plan for future API integrations**

For additional support or questions, refer to the system documentation or contact your administrator.
