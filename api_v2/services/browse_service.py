"""Browse functionality for API v2/BASE 2."""

from __future__ import annotations

import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from shared_api.config.registry import APIRegistry, api_registry
from shared_api.core.exceptions import (
    ConfigurationError,
    HTTPClientError,
    SharedAPIException,
    ValidationError,
)

from ..config.api_config import create_api_v2_configuration


@dataclass
class APIV2BrowseParams:
    """Parameters supported by the API v2 browse endpoints."""

    page: int = 1
    limit: int = 10
    base: str = ""
    bank: str = ""
    bin: str = ""
    country: str = ""
    state: str = ""
    city: str = ""
    brand: str = ""
    type: str = ""
    level: str = ""
    zip: str = ""
    price_from: float = 0
    price_to: float = 500
    zip_check: bool = False
    address: bool = False
    phone: bool = False
    email: bool = False
    without_cvv: bool = False
    refundable: bool = False
    expire_this_month: bool = False
    dob: bool = False
    ssn: bool = False
    mmn: bool = False
    ip: bool = False
    dl: bool = False
    ua: bool = False
    discount: bool = False

    def _bool_to_str(self, value: bool) -> str:
        return "true" if value else "false"

    def _format_numeric(self, value: float) -> str:
        try:
            numeric = float(value)
        except (TypeError, ValueError):
            numeric = 0.0
        if numeric.is_integer():
            return str(int(numeric))
        return f"{numeric:.2f}".rstrip("0").rstrip(".")

    def to_query_pairs(self, *, include_pagination: bool = True) -> List[tuple[str, str]]:
        """Return ordered key/value pairs suitable for query strings."""
        pairs: List[tuple[str, str]] = []
        if include_pagination:
            pairs.append(("page", str(max(1, int(self.page or 1)))))
            pairs.append(("limit", str(max(1, int(self.limit or 10)))))

        def add(key: str, value: Any) -> None:
            text = "" if value in (None, "") else str(value)
            pairs.append((key, text))

        add("base", self.base)
        add("bank", self.bank)
        add("bin", self.bin)
        add("country", self.country)
        add("state", self.state)
        add("city", self.city)
        add("brand", self.brand)
        add("type", self.type)
        add("level", self.level)
        add("zip", self.zip)
        pairs.append(("priceFrom", self._format_numeric(self.price_from)))
        pairs.append(("priceTo", self._format_numeric(self.price_to)))

        boolean_map = {
            "zipCheck": self.zip_check,
            "address": self.address,
            "phone": self.phone,
            "email": self.email,
            "withoutcvv": self.without_cvv,
            "refundable": self.refundable,
            "expirethismonth": self.expire_this_month,
            "dob": self.dob,
            "ssn": self.ssn,
            "mmn": self.mmn,
            "ip": self.ip,
            "dl": self.dl,
            "ua": self.ua,
            "discount": self.discount,
        }
        for key, value in boolean_map.items():
            pairs.append((key, self._bool_to_str(bool(value))))

        return pairs

    def to_filters_only(self) -> List[tuple[str, str]]:
        """Return key/value pairs excluding pagination controls."""
        return self.to_query_pairs(include_pagination=False)


@dataclass
class APIV2BrowseResponse:
    """Normalized browse response wrapper for API v2."""

    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    raw_response: Optional[Any] = None


class APIV2BrowseService:
    """Service coordinating browse operations for API v2/BASE 2."""

    def __init__(
        self,
        *,
        registry: Optional[APIRegistry] = None,
        config_kwargs: Optional[Dict[str, Any]] = None,
    ) -> None:
        self._registry = registry or api_registry
        self._config_kwargs = config_kwargs or {}
        self._config = create_api_v2_configuration(**self._config_kwargs)
        self._config_name = self._config.name
        self._logger = logging.getLogger(f"{__name__}.APIV2BrowseService")
        self._metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'total_response_time': 0.0,
            'average_response_time': 0.0,
            'last_request_time': None,
            'error_counts': {}
        }
        self._ensure_registered()

    def _ensure_registered(self) -> None:
        """Register configuration with the shared registry if needed."""
        try:
            self._registry.register_api(self._config)
        except (ConfigurationError, ValidationError):
            raise
        except SharedAPIException as exc:
            self._logger.debug(
                "Registry already contains configuration %s: %s", self._config_name, exc
            )

    def _get_client(self):
        try:
            return self._registry.get_client(self._config_name)
        except ConfigurationError:
            # Attempt to re-register then retry once
            self._ensure_registered()
            return self._registry.get_client(self._config_name)

    def _normalize_list_payload(
        self, raw: Any, params: APIV2BrowseParams
    ) -> Dict[str, Any]:
        """Normalize API payload to match API v1 structure."""
        payload: Dict[str, Any] = {}
        if isinstance(raw, dict):
            payload.update(raw)
        else:
            payload["data"] = raw

        payload.setdefault("data", payload.get("items", []))
        if not isinstance(payload.get("data"), list):
            payload["data"] = [payload["data"]]

        payload.setdefault("totalCount", payload.get("total") or len(payload["data"]))
        payload.setdefault("page", params.page)
        payload.setdefault("limit", params.limit)
        payload.setdefault(
            "filters",
            {k: v for k, v in params.to_query_pairs(include_pagination=False)},
        )
        payload["success"] = True
        return payload

    def _get_user_friendly_http_error(self, status_code: int, technical_message: str) -> str:
        """Convert HTTP error to user-friendly message"""
        error_map = {
            400: "Invalid request parameters. Please check your filters and try again.",
            401: "Authentication failed. Please check your API credentials.",
            403: "Access denied. You may not have permission to access this resource.",
            404: "The requested resource was not found. The API endpoint may be unavailable.",
            429: "Too many requests. Please wait a moment before trying again.",
            500: "Server error. Please try again later.",
            502: "Service temporarily unavailable. Please try again later.",
            503: "Service temporarily unavailable. Please try again later.",
            504: "Request timeout. Please try again later."
        }

        user_message = error_map.get(status_code, "An unexpected error occurred. Please try again later.")
        self._logger.debug(f"HTTP {status_code} mapped to user message: {user_message} (technical: {technical_message})")
        return user_message

    def _get_user_friendly_api_error(self, technical_message: str) -> str:
        """Convert API error to user-friendly message"""
        message_lower = technical_message.lower()

        if "timeout" in message_lower or "connection" in message_lower:
            return "Connection timeout. Please check your internet connection and try again."
        elif "authentication" in message_lower or "unauthorized" in message_lower:
            return "Authentication failed. Please check your API credentials."
        elif "rate limit" in message_lower or "too many" in message_lower:
            return "Too many requests. Please wait a moment before trying again."
        elif "not found" in message_lower:
            return "The requested resource was not found."
        else:
            return "Service temporarily unavailable. Please try again later."

    def _record_metrics(self, operation: str, response_time: float, success: bool, error_type: str = None):
        """Record performance metrics for monitoring"""
        try:
            self._metrics['total_requests'] += 1
            self._metrics['total_response_time'] += response_time
            self._metrics['average_response_time'] = (
                self._metrics['total_response_time'] / self._metrics['total_requests']
            )
            self._metrics['last_request_time'] = time.time()

            if success:
                self._metrics['successful_requests'] += 1
            else:
                self._metrics['failed_requests'] += 1
                if error_type:
                    self._metrics['error_counts'][error_type] = (
                        self._metrics['error_counts'].get(error_type, 0) + 1
                    )

            # Log performance metrics periodically
            if self._metrics['total_requests'] % 10 == 0:
                success_rate = (
                    self._metrics['successful_requests'] / self._metrics['total_requests'] * 100
                )
                self._logger.info(
                    f"API v2 Performance Metrics - "
                    f"Total: {self._metrics['total_requests']}, "
                    f"Success Rate: {success_rate:.1f}%, "
                    f"Avg Response Time: {self._metrics['average_response_time']:.2f}s"
                )

        except Exception as e:
            self._logger.error(f"Error recording metrics: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        return self._metrics.copy()

    async def list_items(
        self,
        params: Optional[APIV2BrowseParams] = None,
        *,
        user_id: Optional[str] = None,
    ) -> APIV2BrowseResponse:
        """Fetch BIN catalog items for API v2."""
        start_time = time.time()
        operation = "list_items"

        params = params or APIV2BrowseParams()
        query = params.to_query_pairs(include_pagination=True)
        client = self._get_client()

        try:
            self._logger.info(f"Starting {operation} request for user {user_id}")
            raw = await client.post("list_items", params=query)
            response_time = time.time() - start_time

            normalized = self._normalize_list_payload(raw, params)
            self._record_metrics(operation, response_time, True)

            self._logger.info(f"Completed {operation} successfully in {response_time:.2f}s")
            return APIV2BrowseResponse(
                success=True,
                data=normalized,
                raw_response=raw,
            )
        except HTTPClientError as exc:
            response_time = time.time() - start_time
            self._record_metrics(operation, response_time, False, f"HTTP_{exc.status_code}")
            self._logger.error("API v2 list_items failed: %s", exc, exc_info=True)
            user_error = self._get_user_friendly_http_error(exc.status_code, exc.message)
            return APIV2BrowseResponse(
                success=False,
                error=user_error,
                status_code=exc.status_code,
                raw_response=exc.response_text,
            )
        except SharedAPIException as exc:
            response_time = time.time() - start_time
            self._record_metrics(operation, response_time, False, "SharedAPIException")
            self._logger.error("API v2 list_items error: %s", exc, exc_info=True)
            user_error = self._get_user_friendly_api_error(exc.message)
            return APIV2BrowseResponse(success=False, error=user_error)
        except Exception as exc:
            response_time = time.time() - start_time
            self._record_metrics(operation, response_time, False, "UnexpectedException")
            self._logger.error("Unexpected error in list_items: %s", exc, exc_info=True)
            return APIV2BrowseResponse(
                success=False,
                error="Service temporarily unavailable. Please try again later."
            )

    async def get_filters(
        self,
        filter_name: Optional[str] = None,
        params: Optional[APIV2BrowseParams] = None,
        *,
        user_id: Optional[str] = None,
    ) -> APIV2BrowseResponse:
        """Fetch available filter metadata for API v2 browse views."""
        params = params or APIV2BrowseParams()
        client = self._get_client()
        try:
            query = params.to_filters_only()
            if filter_name:
                query.append(("name", filter_name))

            raw = await client.get("filters", params=query)
            response_data = raw if isinstance(raw, dict) else {"filters": raw}
            response_data.setdefault("success", True)
            return APIV2BrowseResponse(success=True, data=response_data, raw_response=raw)
        except HTTPClientError as exc:
            self._logger.error("API v2 filters failed: %s", exc, exc_info=True)
            user_error = self._get_user_friendly_http_error(exc.status_code, exc.message)
            return APIV2BrowseResponse(
                success=False,
                error=user_error,
                status_code=exc.status_code,
                raw_response=exc.response_text,
            )
        except SharedAPIException as exc:
            self._logger.error("API v2 filters error: %s", exc, exc_info=True)
            user_error = self._get_user_friendly_api_error(exc.message)
            return APIV2BrowseResponse(success=False, error=user_error)
        except Exception as exc:
            self._logger.error("Unexpected error in get_filters: %s", exc, exc_info=True)
            return APIV2BrowseResponse(
                success=False,
                error="Unable to load filters. Please try again later."
            )

    async def list_orders(
        self,
        *,
        page: int = 1,
        limit: int = 10,
    ) -> APIV2BrowseResponse:
        """List orders within BASE 2 scope."""
        client = self._get_client()
        query = [("page", str(max(1, int(page)))), ("limit", str(max(1, int(limit))))]
        try:
            raw = await client.get("orders", params=query)
            data = raw if isinstance(raw, dict) else {"orders": raw}
            data.setdefault("page", page)
            data.setdefault("limit", limit)
            data.setdefault("success", True)
            return APIV2BrowseResponse(success=True, data=data, raw_response=raw)
        except HTTPClientError as exc:
            self._logger.error("API v2 list_orders failed: %s", exc)
            return APIV2BrowseResponse(
                success=False,
                error=exc.message,
                status_code=exc.status_code,
                raw_response=exc.response_text,
            )
        except SharedAPIException as exc:
            self._logger.error("API v2 list_orders error: %s", exc)
            return APIV2BrowseResponse(success=False, error=exc.message)

    async def health_check(self) -> bool:
        """Delegated health check via shared API client."""
        client = self._get_client()
        try:
            return await client.health_check()
        except Exception as exc:  # noqa: BLE001
            self._logger.error("API v2 health_check error: %s", exc)
            return False

    async def close(self) -> None:
        """Close the underlying shared API client session."""
        try:
            client = self._get_client()
            await client.close()
        except Exception:  # noqa: BLE001
            pass


_service_instance: Optional[APIV2BrowseService] = None


def get_api_v2_browse_service() -> APIV2BrowseService:
    """Get singleton instance for API v2 browse service."""
    global _service_instance
    if _service_instance is None:
        _service_instance = APIV2BrowseService()
    return _service_instance
