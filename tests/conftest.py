import asyncio
import pytest

try:
    from database.connection import init_database, close_database
except Exception:
    init_database = None
    close_database = None


@pytest.fixture(scope="session", autouse=True)
def _init_db_session_root():
    """Initialize the in-memory database once for the entire test session.

    Ensures services can access collections during import/initialization across all test packages.
    """
    if init_database is None:
        yield
        return

    asyncio.run(init_database())
    try:
        yield
    finally:
        asyncio.run(close_database())
