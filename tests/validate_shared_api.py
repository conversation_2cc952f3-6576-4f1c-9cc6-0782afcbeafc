#!/usr/bin/env python3
"""
Shared API System Validation Script

This script validates that the shared API system is working correctly
and maintains backward compatibility with the existing API v1 implementation.
"""

import asyncio
import sys
import traceback
from typing import Dict, Any, List

def test_imports():
    """Test that all shared API components can be imported"""
    print("🔍 Testing imports...")
    
    try:
        # Core imports
        from shared_api import (
            BaseAPIClient, APIClientProtocol, APIConfigProtocol,
            APIConfiguration, EndpointConfiguration, APIClientFactory,
            ConfigurableHTTPClient
        )
        
        # Configuration imports
        from shared_api.config.api_config import (
            AuthenticationConfiguration, TimeoutConfiguration, RetryConfiguration
        )
        from shared_api.config.client_factory import api_client_factory
        from shared_api.config.registry import api_registry
        
        # Example imports
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        from shared_api.examples.new_api_config import create_new_api_configuration
        
        # Compatibility imports
        from shared_api.compatibility import get_external_api_service, APIv1CompatibilityClient
        
        # Constants and exceptions
        from shared_api.core.constants import <PERSON><PERSON><PERSON>ethod, AuthenticationType
        from shared_api.core.exceptions import ConfigurationError, HTTPClientError
        
        print("✅ All imports successful")
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        traceback.print_exc()
        return False


def test_configuration_creation():
    """Test creating API configurations"""
    print("\n🔧 Testing configuration creation...")
    
    try:
        from shared_api.config.api_config import APIConfiguration, EndpointConfiguration
        from shared_api.core.constants import HTTPMethod
        
        # Test basic configuration
        config = APIConfiguration(
            name="test_api",
            base_url="https://api.test.com",
            endpoints={
                "test_endpoint": EndpointConfiguration(
                    name="test_endpoint",
                    path="/test",
                    method=HTTPMethod.GET
                )
            }
        )
        
        assert config.name == "test_api"
        assert config.base_url == "https://api.test.com"
        assert "test_endpoint" in config.endpoints
        assert config.validate() is True
        
        # Test endpoint URL generation
        url = config.get_endpoint_url("test_endpoint")
        assert url == "https://api.test.com/test"
        
        print("✅ Configuration creation successful")
        return True
        
    except Exception as e:
        print(f"❌ Configuration creation failed: {e}")
        traceback.print_exc()
        return False


def test_api_v1_configuration():
    """Test API v1 configuration creation"""
    print("\n🔧 Testing API v1 configuration...")
    
    try:
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        from shared_api.core.constants import AuthenticationType
        
        # Test without authentication
        config = create_api_v1_configuration()
        assert config.name == "api_v1"
        assert "list_items" in config.endpoints
        assert "cart_view" in config.endpoints
        assert "user_info" in config.endpoints
        
        # Test with authentication
        config_with_auth = create_api_v1_configuration(
            login_token="test-token"
        )
        assert config_with_auth.auth_config.type == AuthenticationType.BEARER_TOKEN
        
        print("✅ API v1 configuration successful")
        return True
        
    except Exception as e:
        print(f"❌ API v1 configuration failed: {e}")
        traceback.print_exc()
        return False


def test_client_factory():
    """Test API client factory"""
    print("\n🏭 Testing client factory...")
    
    try:
        from shared_api.config.client_factory import APIClientFactory
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        from shared_api.http.client import ConfigurableHTTPClient
        
        factory = APIClientFactory()
        config = create_api_v1_configuration()
        
        # Test client creation
        client = factory.create_client(config)
        assert isinstance(client, ConfigurableHTTPClient)
        assert client.config == config
        
        # Test configuration registration
        factory.register_configuration(config)
        client2 = factory.create_client("api_v1")
        assert isinstance(client2, ConfigurableHTTPClient)
        
        print("✅ Client factory successful")
        return True
        
    except Exception as e:
        print(f"❌ Client factory failed: {e}")
        traceback.print_exc()
        return False


def test_api_registry():
    """Test API registry"""
    print("\n📋 Testing API registry...")
    
    try:
        from shared_api.config.registry import APIRegistry
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        from shared_api.examples.new_api_config import create_new_api_configuration
        
        registry = APIRegistry()
        
        # Register APIs
        api_v1_config = create_api_v1_configuration()
        new_api_config = create_new_api_configuration()
        
        registry.register_api(api_v1_config)
        registry.register_api(new_api_config)
        
        # Test listing
        apis = registry.list_apis()
        assert len(apis) >= 2
        assert any(api["name"] == "api_v1" for api in apis)
        assert any(api["name"] == "example_api" for api in apis)
        
        # Test client creation
        client = registry.get_client("api_v1")
        assert client is not None
        
        print("✅ API registry successful")
        return True
        
    except Exception as e:
        print(f"❌ API registry failed: {e}")
        traceback.print_exc()
        return False


def test_compatibility_layer():
    """Test backward compatibility layer"""
    print("\n🔄 Testing compatibility layer...")
    
    try:
        from shared_api.compatibility import get_external_api_service, APIv1CompatibilityClient
        
        # Test client creation
        client = get_external_api_service(
            base_url="https://test.com/api",
            login_token="test-token"
        )
        
        assert isinstance(client, APIv1CompatibilityClient)
        assert client.base_url == "https://test.com/api"
        assert client.login_token == "test-token"
        
        # Test that all expected methods exist
        expected_methods = [
            'list_items', 'add_to_cart', 'view_cart', 'delete_from_cart',
            'get_user_info', 'checkout', 'check_order'
        ]
        
        for method in expected_methods:
            assert hasattr(client, method), f"Missing method: {method}"
        
        print("✅ Compatibility layer successful")
        return True
        
    except Exception as e:
        print(f"❌ Compatibility layer failed: {e}")
        traceback.print_exc()
        return False


def test_configuration_serialization():
    """Test configuration serialization"""
    print("\n💾 Testing configuration serialization...")
    
    try:
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        from shared_api.config.api_config import APIConfiguration
        
        # Create configuration
        original_config = create_api_v1_configuration()
        
        # Serialize to dict
        config_dict = original_config.to_dict()
        assert isinstance(config_dict, dict)
        assert config_dict["name"] == "api_v1"
        assert "endpoints" in config_dict
        
        # Deserialize back
        restored_config = APIConfiguration.from_dict(config_dict)
        assert restored_config.name == original_config.name
        assert restored_config.base_url == original_config.base_url
        assert len(restored_config.endpoints) == len(original_config.endpoints)
        
        print("✅ Configuration serialization successful")
        return True
        
    except Exception as e:
        print(f"❌ Configuration serialization failed: {e}")
        traceback.print_exc()
        return False


async def test_async_functionality():
    """Test async functionality"""
    print("\n⚡ Testing async functionality...")
    
    try:
        from shared_api.compatibility import get_external_api_service
        
        # Test async context manager
        client = get_external_api_service()
        
        async with client:
            # Test that client can be used in async context
            assert not client._closed

        # Test that client has the _closed attribute and async context works
        assert hasattr(client, '_closed')
        
        print("✅ Async functionality successful")
        return True
        
    except Exception as e:
        print(f"❌ Async functionality failed: {e}")
        traceback.print_exc()
        return False


def test_error_handling():
    """Test error handling"""
    print("\n🚨 Testing error handling...")
    
    try:
        from shared_api.core.exceptions import ConfigurationError, HTTPClientError
        from shared_api.config.client_factory import APIClientFactory
        
        factory = APIClientFactory()
        
        # Test configuration error
        try:
            factory.create_client("nonexistent_api")
            assert False, "Should have raised ConfigurationError"
        except ConfigurationError:
            pass  # Expected
        
        # Test client type error
        from shared_api.examples.api_v1_config import create_api_v1_configuration
        config = create_api_v1_configuration()
        
        try:
            factory.create_client(config, client_type="nonexistent_type")
            assert False, "Should have raised ConfigurationError"
        except ConfigurationError:
            pass  # Expected
        
        print("✅ Error handling successful")
        return True
        
    except Exception as e:
        print(f"❌ Error handling failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all validation tests"""
    print("🚀 Starting Shared API System Validation\n")
    
    tests = [
        test_imports,
        test_configuration_creation,
        test_api_v1_configuration,
        test_client_factory,
        test_api_registry,
        test_compatibility_layer,
        test_configuration_serialization,
        test_error_handling,
    ]
    
    async_tests = [
        test_async_functionality,
    ]
    
    results = []
    
    # Run synchronous tests
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Run asynchronous tests
    for test in async_tests:
        try:
            result = asyncio.run(test())
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print(f"\n📊 Validation Summary:")
    print(f"   Passed: {passed}/{total}")
    print(f"   Failed: {total - passed}/{total}")
    
    if passed == total:
        print("\n🎉 All tests passed! The shared API system is working correctly.")
        print("\n✨ Benefits of the new system:")
        print("   • Reusable API client architecture")
        print("   • Configuration-driven approach")
        print("   • Enhanced error handling and logging")
        print("   • Full backward compatibility")
        print("   • Easy to add new APIs")
        print("   • Comprehensive testing support")
        
        print("\n🚀 Next steps:")
        print("   1. Start using the compatibility layer: from shared_api.compatibility import get_external_api_service")
        print("   2. Gradually migrate to new patterns")
        print("   3. Use the shared system for all new APIs")
        print("   4. See SHARED_API_MIGRATION_GUIDE.md for detailed migration instructions")
        
        return 0
    else:
        print(f"\n❌ {total - passed} tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
