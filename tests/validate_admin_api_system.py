#!/usr/bin/env python3
"""
Comprehensive Validation Script for Admin API Management System

This script validates that the entire admin API management system is working correctly,
including integration with the shared API infrastructure.
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Tuple
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

logger = logging.getLogger(__name__)


class AdminAPISystemValidator:
    """
    Comprehensive validator for the admin API management system
    
    Tests all components including:
    - Database models and storage
    - Admin service layer
    - Integration with shared API system
    - Health monitoring
    - UI components
    """
    
    def __init__(self):
        self.test_results = []
        self.passed_tests = 0
        self.failed_tests = 0
    
    async def run_all_validations(self) -> Dict[str, Any]:
        """Run all validation tests"""
        logger.info("Starting comprehensive admin API system validation...")

        try:
            # Initialize database connection
            await self._initialize_database()

            # Initialize shared API system
            await self._initialize_shared_api_system()
            # Test 1: Database Models and Storage
            await self._test_database_models()
            
            # Test 2: Admin Service Layer
            await self._test_admin_service_layer()
            
            # Test 3: Shared API Integration
            await self._test_shared_api_integration()
            
            # Test 4: Health Monitoring
            await self._test_health_monitoring()
            
            # Test 5: Configuration Management
            await self._test_configuration_management()
            
            # Test 6: UI Components
            await self._test_ui_components()
            
            # Test 7: Migration and Cleanup Scripts
            await self._test_migration_scripts()
            
            # Generate summary
            summary = {
                "status": "completed",
                "total_tests": len(self.test_results),
                "passed": self.passed_tests,
                "failed": self.failed_tests,
                "success_rate": (self.passed_tests / len(self.test_results) * 100) if self.test_results else 0,
                "test_results": self.test_results,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Validation completed: {self.passed_tests}/{len(self.test_results)} tests passed")
            return summary
            
        except Exception as e:
            logger.error(f"Validation failed: {e}")
            return {
                "status": "failed",
                "error": str(e),
                "passed": self.passed_tests,
                "failed": self.failed_tests
            }

    async def _initialize_database(self):
        """Initialize database connection"""
        try:
            from database.connection import init_database
            await init_database()
            logger.info("Database connection initialized")
        except Exception as e:
            logger.warning(f"Could not initialize database: {e}")
            # Continue with validation - some tests may still work

    async def _initialize_shared_api_system(self):
        """Initialize shared API system"""
        try:
            from admin.services.shared_api_integration import initialize_shared_api_integration
            success = await initialize_shared_api_integration()
            if success:
                logger.info("Shared API system initialized")
            else:
                logger.warning("Shared API system initialization failed")
        except Exception as e:
            logger.warning(f"Could not initialize shared API system: {e}")
            # Continue with validation
    
    async def _test_database_models(self):
        """Test database models and storage functionality"""
        test_name = "Database Models and Storage"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.models.api_config_storage import (
                get_admin_api_service,
                AdminAPIConfiguration,
                APIConfigEnvironment,
                APIConfigStatus
            )
            from shared_api.config.api_config import APIConfiguration
            
            admin_storage = get_admin_api_service()
            
            # Test creating a configuration
            test_shared_config = APIConfiguration(
                name="test_api_validation",
                base_url="https://api.test.com",
                endpoints={},
                authentication=None
            )
            
            admin_config = await admin_storage.create_api_config(
                shared_config=test_shared_config,
                created_by="validation_test",
                display_name="Test API for Validation",
                description="Test configuration for validation",
                environment=APIConfigEnvironment.DEVELOPMENT,
                status=APIConfigStatus.ACTIVE
            )
            
            # Test retrieving the configuration
            retrieved_config = await admin_storage.get_api_config("test_api_validation")
            
            # Test listing configurations
            configs = await admin_storage.list_api_configs()
            
            # Test updating configuration
            success = await admin_storage.update_api_config(
                name="test_api_validation",
                shared_config=test_shared_config,
                updated_by="validation_test",
                changes_description="Test update"
            )
            
            # Cleanup
            await admin_storage.delete_api_config("test_api_validation")
            
            self._record_test_result(test_name, True, "All database operations successful")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Database test failed: {str(e)}")
    
    async def _test_admin_service_layer(self):
        """Test admin service layer functionality"""
        test_name = "Admin Service Layer"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.services.shared_api_admin_service import get_shared_api_admin_service
            
            admin_service = get_shared_api_admin_service()
            
            # Test creating API configuration
            success, message, admin_config = await admin_service.create_api_configuration(
                name="test_service_api",
                base_url="https://service.test.com",
                created_by="validation_test",
                display_name="Test Service API",
                description="Test API for service validation",
                auth_type="none",
                environment="development"
            )
            
            if not success:
                raise Exception(f"Failed to create API: {message}")
            
            # Test listing configurations
            api_configs = await admin_service.list_api_configurations()
            
            # Test getting API details
            api_details = await admin_service.get_api_details("test_service_api")
            
            if not api_details:
                raise Exception("Failed to get API details")
            
            # Test updating configuration
            success, message = await admin_service.update_api_configuration(
                name="test_service_api",
                updated_by="validation_test",
                base_url="https://updated.service.test.com"
            )
            
            if not success:
                raise Exception(f"Failed to update API: {message}")
            
            # Cleanup
            await admin_service.admin_storage.delete_api_config("test_service_api")
            
            self._record_test_result(test_name, True, "All admin service operations successful")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Admin service test failed: {str(e)}")
    
    async def _test_shared_api_integration(self):
        """Test integration with shared API system"""
        test_name = "Shared API Integration"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.services.shared_api_integration import get_shared_api_integration_service
            from shared_api.config.registry import api_registry
            
            integration_service = get_shared_api_integration_service()
            
            # Test initialization
            success = await integration_service.initialize_integration()
            if not success:
                raise Exception("Failed to initialize integration")
            
            # Test registry status
            registry_status = await integration_service.get_registry_status()
            if not registry_status.get("initialized"):
                raise Exception("Registry not properly initialized")
            
            # Test configuration sync
            successful_syncs, total_configs = await integration_service.sync_all_configurations()
            
            self._record_test_result(test_name, True, f"Integration successful, synced {successful_syncs}/{total_configs} configs")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Integration test failed: {str(e)}")
    
    async def _test_health_monitoring(self):
        """Test health monitoring functionality"""
        test_name = "Health Monitoring"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.services.api_health_monitoring import get_api_health_monitoring_service
            
            health_service = get_api_health_monitoring_service()
            
            # Test health summary
            health_summary = await health_service.get_health_summary()
            
            if "total_apis" not in health_summary:
                raise Exception("Invalid health summary format")
            
            # Test checking all APIs health (if any exist)
            health_results = await health_service.check_all_apis_health()
            
            self._record_test_result(test_name, True, f"Health monitoring working, {len(health_results)} APIs checked")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Health monitoring test failed: {str(e)}")
    
    async def _test_configuration_management(self):
        """Test configuration management features"""
        test_name = "Configuration Management"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.services.shared_api_integration import get_shared_api_integration_service
            from admin.services.shared_api_admin_service import get_shared_api_admin_service
            
            integration_service = get_shared_api_integration_service()
            admin_service = get_shared_api_admin_service()
            
            # Create test configuration
            success, message, admin_config = await admin_service.create_api_configuration(
                name="test_config_mgmt",
                base_url="https://config.test.com",
                created_by="validation_test",
                auth_type="api_key",
                auth_data={"api_key": "test-key", "api_key_header": "X-Test-Key"}
            )
            
            if not success:
                raise Exception(f"Failed to create test config: {message}")
            
            # Test export
            export_data = await integration_service.export_configuration("test_config_mgmt")
            if not export_data:
                raise Exception("Failed to export configuration")
            
            # Test import (with different name)
            export_data["admin_config"]["name"] = "test_config_imported"
            export_data["shared_config"]["name"] = "test_config_imported"
            
            success, message = await integration_service.import_configuration(
                import_data=export_data,
                imported_by="validation_test"
            )
            
            if not success:
                raise Exception(f"Failed to import configuration: {message}")
            
            # Cleanup
            await admin_service.admin_storage.delete_api_config("test_config_mgmt")
            await admin_service.admin_storage.delete_api_config("test_config_imported")
            
            self._record_test_result(test_name, True, "Configuration management operations successful")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Configuration management test failed: {str(e)}")
    
    async def _test_ui_components(self):
        """Test UI components"""
        test_name = "UI Components"
        logger.info(f"Testing: {test_name}")
        
        try:
            from admin.ui.api_management_ui import APIManagementUI
            
            ui = APIManagementUI()
            
            # Test main menu formatting
            test_configs = [
                {
                    "name": "test_api",
                    "display_name": "Test API",
                    "base_url": "https://test.com",
                    "enabled": True,
                    "health_status": "healthy"
                }
            ]
            
            main_menu_text = ui.format_main_menu(test_configs)
            if not main_menu_text or "API Management System" not in main_menu_text:
                raise Exception("Main menu formatting failed")
            
            # Test keyboard creation
            keyboard = ui.create_main_menu_keyboard()
            if not keyboard or not keyboard.inline_keyboard:
                raise Exception("Keyboard creation failed")
            
            # Test API list formatting
            api_list_text = ui.format_api_list(test_configs)
            if not api_list_text or "API Configurations" not in api_list_text:
                raise Exception("API list formatting failed")
            
            self._record_test_result(test_name, True, "UI components working correctly")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"UI components test failed: {str(e)}")
    
    async def _test_migration_scripts(self):
        """Test migration and cleanup scripts"""
        test_name = "Migration Scripts"
        logger.info(f"Testing: {test_name}")
        
        try:
            # Test that migration script can be imported
            from admin.scripts.migrate_legacy_api_config import LegacyAPIMigrator
            
            migrator = LegacyAPIMigrator()
            
            # Test dry run migration (should not fail even with no legacy configs)
            results = await migrator.migrate_all_configurations(dry_run=True)
            
            if results["status"] not in ["completed", "failed"]:
                raise Exception("Invalid migration results format")
            
            # Test that cleanup script can be imported
            from admin.scripts.cleanup_legacy_api_code import LegacyCodeCleanup
            
            cleanup_manager = LegacyCodeCleanup()
            
            # Test dry run cleanup
            cleanup_results = await cleanup_manager.cleanup_legacy_code(dry_run=True, create_backup=False)
            
            if cleanup_results["status"] not in ["dry_run_completed", "failed"]:
                raise Exception("Invalid cleanup results format")
            
            self._record_test_result(test_name, True, "Migration scripts working correctly")
            
        except Exception as e:
            self._record_test_result(test_name, False, f"Migration scripts test failed: {str(e)}")
    
    def _record_test_result(self, test_name: str, success: bool, message: str):
        """Record the result of a test"""
        result = {
            "test_name": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        self.test_results.append(result)
        
        if success:
            self.passed_tests += 1
            logger.info(f"✅ {test_name}: {message}")
        else:
            self.failed_tests += 1
            logger.error(f"❌ {test_name}: {message}")


async def main():
    """Main validation function"""
    import argparse
    import json
    
    parser = argparse.ArgumentParser(description="Validate Admin API Management System")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("--output", type=str, help="Save results to JSON file")
    
    args = parser.parse_args()
    
    # Configure logging
    log_level = logging.DEBUG if args.verbose else logging.INFO
    logging.basicConfig(
        level=log_level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Run validation
    validator = AdminAPISystemValidator()
    results = await validator.run_all_validations()
    
    # Print results
    print("\n" + "="*60)
    print("ADMIN API SYSTEM VALIDATION RESULTS")
    print("="*60)
    print(f"Status: {results['status']}")
    print(f"Total Tests: {results.get('total_tests', 0)}")
    print(f"Passed: {results.get('passed', 0)}")
    print(f"Failed: {results.get('failed', 0)}")
    print(f"Success Rate: {results.get('success_rate', 0):.1f}%")
    
    if results.get('test_results'):
        print("\nDetailed Results:")
        for result in results['test_results']:
            status = "✅" if result['success'] else "❌"
            print(f"  {status} {result['test_name']}: {result['message']}")
    
    # Save results if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        print(f"\nResults saved to: {args.output}")
    
    # Return appropriate exit code
    return 0 if results['status'] == 'completed' and results.get('failed', 0) == 0 else 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
