#!/usr/bin/env python3
"""Validation script for API v2/BASE 2 implementation."""

import asyncio
import sys
from typing import Any, Dict, List, Optional


def banner(title: str) -> None:
    print(f"\n=== {title} ===")


def test_imports() -> bool:
    banner("Importing modules")
    try:
        from api_v2 import create_api_v2_configuration, APIV2BrowseService  # noqa: F401
        from shared_api.config.registry import register_api_v2  # noqa: F401
        print("✅ Imports successful")
        return True
    except Exception as exc:  # noqa: BLE001
        print(f"❌ Import error: {exc}")
        return False


def test_configuration() -> bool:
    banner("Validating configuration")
    from api_v2 import create_api_v2_configuration

    config = create_api_v2_configuration()
    expected_base = "https://ronaldo-club.to/api/cards/vhq"
    endpoints = {name: ep.path for name, ep in config.endpoints.items()}

    checks = [
        (config.name == "api2", "Config name"),
        (config.base_url == expected_base, "Base URL"),
        ("list_items" in endpoints and endpoints["list_items"] == "/list", "List endpoint"),
        ("filters" in endpoints and endpoints["filters"] == "/filters", "Filters endpoint"),
        ("orders" in endpoints and endpoints["orders"] == "/orders", "Orders endpoint"),
    ]

    succeeded = True
    for ok, label in checks:
        if ok:
            print(f"✅ {label}")
        else:
            print(f"❌ {label} failed")
            succeeded = False
    return succeeded


class StubClient:
    def __init__(self, response: Optional[Dict[str, Any]] = None):
        self.records: List[tuple[str, str, Any]] = []
        self.response = response or {"data": [], "totalCount": 0}

    async def post(self, endpoint: str, *, params=None, **kwargs):
        self.records.append(("POST", endpoint, params))
        return self.response

    async def get(self, endpoint: str, *, params=None, **kwargs):
        self.records.append(("GET", endpoint, params))
        return self.response

    async def health_check(self) -> bool:
        return True

    async def close(self) -> None:
        return None


class StubRegistry:
    def __init__(self, client: StubClient):
        self.client = client

    def register_api(self, config):
        self.config = config

    def get_client(self, name: str):
        return self.client


async def test_browse_service_queries() -> bool:
    banner("Validating browse service query construction")
    from api_v2.services.browse_service import APIV2BrowseParams, APIV2BrowseService

    client = StubClient({"data": [1], "totalCount": 1})
    service = APIV2BrowseService(registry=StubRegistry(client))

    params = APIV2BrowseParams(page=3, limit=12, bank="META", zip_check=True)
    resp = await service.list_items(params)

    ok = resp.success and resp.data["page"] == 3 and resp.data["limit"] == 12
    if ok:
        print("✅ list_items response normalized")
    else:
        print("❌ list_items normalization failed")

    method, endpoint, query = client.records[0]
    no_double_amp = all("&&" not in f"{key}={value}" for key, value in query)
    checks = [
        (method == "POST", "HTTP method"),
        (endpoint == "list_items", "Endpoint name"),
        (no_double_amp, "No double ampersands"),
        (("zipCheck", "true") in query, "Boolean coercion"),
    ]

    success = ok
    for passed, label in checks:
        if passed:
            print(f"✅ {label}")
        else:
            print(f"❌ {label} failed")
            success = False

    client.records.clear()
    await service.get_filters("bank", params)
    _, _, filter_query = client.records[0]
    pagination_removed = all(key not in ("page", "limit") for key, _ in filter_query)
    if pagination_removed:
        print("✅ Filters omit pagination parameters")
    else:
        print("❌ Filters still include pagination")
        success = False

    return success


async def main() -> int:
    results = [
        test_imports(),
        test_configuration(),
        await test_browse_service_queries(),
    ]
    if all(results):
        print("\n🎉 API v2 validation passed")
        return 0
    print("\n⚠️ API v2 validation reported issues")
    return 1


if __name__ == "__main__":
    try:
        raise SystemExit(asyncio.run(main()))
    except KeyboardInterrupt:
        print("Interrupted")
        raise SystemExit(130)
